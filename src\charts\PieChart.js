export class PieChart {
  constructor(editor, config = {}) {
    this.editor = editor;
    this.config = {
      centerX: config.centerX || 400,
      centerY: config.centerY || 350,
      radius: config.radius || 120,
      colors: config.colors || [
        "#FF6B6B",
        "#4ECDC4",
        "#45B7D1",
        "#96CEB4",
        "#FFEAA7",
        "#DDA0DD",
        "#98D8C8",
        "#F7DC6F",
        "#BB8FCE",
        "#85C1E9",
      ],
      ...config,
    };

    this.elements = [];
    this.isInitialized = false;
  }

  create(alocacao, total) {
    this.clear();

    if (!alocacao || !Array.isArray(alocacao) || alocacao.length === 0) {
      console.error("❌ [PieChart] Invalid alocacao data:", alocacao);
      return;
    }

    if (!total || total <= 0) {
      console.error("❌ [PieC<PERSON>] Invalid total value:", total);
      return;
    }

    const { centerX, centerY, radius, colors } = this.config;
    let currentAngle = -Math.PI / 2;

    alocacao.forEach((ativo, index) => {
      const percentage = (ativo.value / total) * 100;
      const sliceAngle = (percentage / 100) * 2 * Math.PI;

      const slice = this.createSlice(
        currentAngle,
        sliceAngle,
        colors[index % colors.length]
      );

      const label = this.createLabel(
        currentAngle,
        sliceAngle,
        ativo.percentageFormatted
      );

      const legendEntry = this.createLegendEntry(
        index,
        ativo,
        colors[index % colors.length]
      );

      this.elements.push({ slice, label, legendEntry });

      currentAngle += sliceAngle;
    });

    this.createChartTitle();
    this.createChartBorder();

    this.isInitialized = true;
  }

  createSlice(startAngle, sliceAngle, color) {
    const { centerX, centerY, radius } = this.config;

    try {
      const slicePoints = [];
      const segments = Math.max(
        8,
        Math.ceil((sliceAngle / (2 * Math.PI)) * 32)
      );

      slicePoints.push([centerX, centerY]);

      for (let i = 0; i <= segments; i++) {
        const angle = startAngle + (sliceAngle * i) / segments;
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        slicePoints.push([x, y]);
      }

      slicePoints.push([centerX, centerY]);

      const slice = this.editor.factory.createLine(slicePoints, true);

      slice.fillColor = color;
      slice.strokeColor = "#FFFFFF";
      slice.strokeWidth = 2;
      slice.movable = true;
      slice.selectable = true;

      this.editor.actions.insert(slice);

      return slice;
    } catch (error) {
      console.error("❌ [PieChart] Error creating slice:", error);

      try {
        const offset = Math.random() * 100 - 50;
        const testRect = [
          [centerX - 25 + offset, centerY - 25 + offset],
          [centerX + 25 + offset, centerY + 25 + offset],
        ];

        const slice = this.editor.factory.createRectangle(testRect);
        slice.fillColor = color;
        slice.strokeColor = "#FFFFFF";
        slice.strokeWidth = 2;
        slice.movable = true;
        slice.selectable = true;
        this.editor.actions.insert(slice);

        return slice;
      } catch (fallbackError) {
        console.error("❌ [PieChart] Fallback also failed:", fallbackError);
        return null;
      }
    }
  }

  createLabel(startAngle, sliceAngle, percentage) {
    const { centerX, centerY, radius } = this.config;
    const labelAngle = startAngle + sliceAngle / 2;
    const labelRadius = radius * 0.7;
    const labelX = centerX + labelRadius * Math.cos(labelAngle);
    const labelY = centerY + labelRadius * Math.sin(labelAngle);

    try {
      const textRect = [
        [labelX - 30, labelY - 10],
        [labelX + 30, labelY + 10],
      ];

      const label = this.editor.factory.createText(textRect, percentage);
      label.fontSize = 12;
      label.fontWeight = "bold";
      label.fillColor = "#FFFFFF";
      label.movable = true;
      label.selectable = true;

      this.editor.actions.insert(label);

      return label;
    } catch (error) {
      console.error("❌ [PieChart] Error creating label:", error);
      return null;
    }
  }

  createLegendEntry(index, ativo, color) {
    const legendY = 200 + index * 25;

    try {
      const legendColorRect = [
        [650, legendY],
        [670, legendY + 15],
      ];

      const legendColor = this.editor.factory.createRectangle(legendColorRect);
      legendColor.fillColor = color;
      legendColor.strokeColor = "#333";
      legendColor.movable = true;
      legendColor.selectable = true;

      this.editor.actions.insert(legendColor);

      const textRect = [
        [680, legendY],
        [880, legendY + 15],
      ];

      const legendText = this.editor.factory.createText(
        textRect,
        `${ativo.product}: ${ativo.valueFormatted}`
      );
      legendText.fontSize = 12;
      legendText.fillColor = "#333";
      legendText.movable = true;
      legendText.selectable = true;

      this.editor.actions.insert(legendText);

      return { color: legendColor, text: legendText };
    } catch (error) {
      console.error("❌ [PieChart] Error creating legend entry:", error);
      return null;
    }
  }

  createChartTitle() {
    const { centerX, centerY } = this.config;

    const titleRect = [
      [centerX - 80, centerY - 160],
      [centerX + 80, centerY - 140],
    ];

    const chartTitle = this.editor.factory.createText(
      titleRect,
      "📊 Distribuição do Patrimônio"
    );
    chartTitle.fontSize = 16;
    chartTitle.fontWeight = "bold";
    chartTitle.fillColor = "#333";
    chartTitle.movable = true;
    chartTitle.selectable = true;

    this.editor.actions.insert(chartTitle);

    console.log("📊 [PieChart] Chart title created and inserted");
    this.elements.push({ title: chartTitle });
  }

  createChartBorder() {
    const { centerX, centerY, radius } = this.config;

    try {
      const borderPoints = [];
      const segments = 64;

      for (let i = 0; i <= segments; i++) {
        const angle = (2 * Math.PI * i) / segments;
        const x = centerX + (radius + 5) * Math.cos(angle);
        const y = centerY + (radius + 5) * Math.sin(angle);
        borderPoints.push([x, y]);
      }

      const border = this.editor.factory.createLine(borderPoints, true);
      border.fillColor = "transparent";
      border.strokeColor = "#333";
      border.strokeWidth = 2;
      border.movable = true;
      border.selectable = true;

      this.editor.actions.insert(border);

      console.log("🔲 [PieChart] Chart border created and inserted");
      this.elements.push({ border });
    } catch (error) {
      console.error("❌ [PieChart] Error creating chart border:", error);

      try {
        const borderRect = [
          [centerX - radius - 10, centerY - radius - 10],
          [centerX + radius + 10, centerY + radius + 10],
        ];

        const border = this.editor.factory.createRectangle(borderRect);
        border.fillColor = "transparent";
        border.strokeColor = "#333";
        border.strokeWidth = 2;
        border.movable = true;
        border.selectable = true;

        this.editor.actions.insert(border);
        console.log("🔲 [PieChart] Fallback rectangular border created");
        this.elements.push({ border });
      } catch (fallbackError) {
        console.error(
          "❌ [PieChart] Fallback border also failed:",
          fallbackError
        );
      }
    }
  }

  clear() {
    this.elements = [];
    this.isInitialized = false;
  }

  update(alocacao, total) {
    this.create(alocacao, total);
  }

  getConfig() {
    return { ...this.config };
  }

  updateConfig(newConfig) {
    console.log("🎛️ [PieChart] Updating config:", newConfig);
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    console.log(
      "🎛️ [PieChart] Config updated from:",
      oldConfig,
      "to:",
      this.config
    );
    return this.config;
  }

  recreateWithConfig(alocacao, total) {
    console.log(
      "🔄 [PieChart] Recreating chart with current config:",
      this.config
    );
    this.create(alocacao, total);
  }
}
