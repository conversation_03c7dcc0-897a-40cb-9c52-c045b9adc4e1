# 🔧 DOCUMENTAÇÃO TÉCNICA - SISTEMA GRÁFICO DE TORTA

## 🏗️ **ARQUITETURA DO SISTEMA**

### **Fluxo de Dados**

```
app-calc-reino (formulário) 
    ↓ (HTTP POST)
servidor local (:3000/api/data)
    ↓ (polling a cada 2s)
dgm-canvas DataManager
    ↓ (event: dataReceived) 
useDGMCanvas hook
    ↓ (updateCanvasWithData)
PieChart.js (criação visual)
    ↓ (DGM.js API)
Canvas Visual no Navegador
```

---

## 📊 **COMPONENTES PRINCIPAIS**

### **1. DataManager.js**

**Função**: Gerencia comunicação e polling de dados

```javascript
// Localização: src/utils/DataManager.js
// Status: ✅ FUNCIONANDO

// Principais métodos:
- startPolling()      // Inicia polling automático
- checkForData()      // Verifica novos dados no servidor  
- validateData()      // Valida estrutura dos dados
- notifyListeners()   // Dispara events para subscribers

// Event listeners implementados:
- addEventListener("dataReceived", callback)
- addEventListener("error", callback)
```

### **2. PieChart.js**

**Função**: Cria elementos visuais do gráfico

```javascript
// Localização: src/charts/PieChart.js  
// Status: ✅ FUNCIONANDO (com formas básicas)

// Principais métodos:
- create(alocacao, total)    // Método principal de criação
- createSlice()              // Cria fatias individuais
- createLabel()              // Cria labels com percentuais  
- createLegendEntry()        // Cria entradas da legenda
- createChartTitle()         // Cria título do gráfico
- createChartBorder()        // Cria borda do gráfico

// Configurações atuais:
centerX: 400, centerY: 350, radius: 120
```

### **3. useDGMCanvas.js**

**Função**: Hook que conecta dados ao canvas

```javascript
// Localização: src/utils/useDGMCanvas.js
// Status: ✅ FUNCIONANDO

// Principais funções:
- handleMount()              // Inicializa editor DGM
- updateCanvasWithData()     // Atualiza canvas com novos dados
- createVisualizationHeader() // Cria cabeçalho com info do usuário
- createTimestamp()          // Cria timestamp de atualização

// Event binding:
dataManager.addEventListener("dataReceived", handler)
```

### **4. App.jsx**

**Função**: Componente principal que orquestra tudo

```javascript
// Localização: src/App.jsx  
// Status: ✅ FUNCIONANDO

// Componentes renderizados:
- StatusPanel      // Panel de debug/status
- DGMEditor        // Canvas principal  
- WelcomeScreen    // Tela inicial
- Botão de teste   // Para debug manual

// Estados gerenciados:
- receivedData     // Dados recebidos do servidor
- lastUpdate      // Timestamp da última atualização
```

---

## 🎨 **ESTRUTURA VISUAL ATUAL**

### **Layout do Canvas (1200x800)**

```
┌─────────────────────────────────────────────────────────┐
│ [50,30] Patrimônio de João                              │
│                                                         │
│ [50,80] 💰 Total: R$ 9.000                             │
│         📊 Alocado: R$ 9.000 (100%)                    │
│         💵 Restante: R$ 0                               │
│                                                         │
│              [400,350] 🥧 GRÁFICO                       │
│           ┌─────────────────┐                          │
│           │  [320,190] 📊   │  [650,200] 🟥 Poupança   │
│           │ Distribuição do │  [650,225] 🟦 Previdência│
│           │   Patrimônio    │  [650,250] 🟩 COE        │
│           │                 │  [650,275] 🟨 Cripto     │
│           │   ●─────────    │                          │
│           │  /|\       ●    │                          │
│           │ Labels 36%      │                          │
│           │   ●─────────    │                          │
│           └─────────────────┘                          │
│                                                         │
│ [50,550] Atualizado em: 04/08/2025 20:46:45           │
└─────────────────────────────────────────────────────────┘
```

### **Elementos Criados por Fatia**

```javascript
// Para cada item da alocação (ex: Poupança 36%):

1. FATIA (slice):
   - Tipo: Line/Polygon com pontos calculados
   - Cor: colors[index] (ex: #FF6B6B)  
   - Posição: Centro [400,350] + ângulos
   - Status: ✅ Criado (formato retangular temporário)

2. LABEL (percentual):
   - Tipo: Text
   - Conteúdo: "36%" 
   - Posição: Sobre a fatia (calculada por ângulo)
   - Status: ✅ Funcionando

3. LEGENDA:
   - Quadrado colorido: Rectangle [650,200][670,215]
   - Texto: "Poupança: R$ 3.200,00"
   - Status: ✅ Funcionando
```

---

## 🔧 **APIs DO DGM.JS UTILIZADAS**

### **Factory Methods (✅ Testados e Funcionando)**

```javascript
// Criação de elementos:
editor.factory.createRectangle([[x1,y1], [x2,y2]])
editor.factory.createText([[x1,y1], [x2,y2]], "texto")  
editor.factory.createLine(points[], closed=true)

// Inserção no canvas:
editor.actions.insert(element)

// Propriedades dos elementos:
element.fillColor = "#FF6B6B"
element.strokeColor = "#FFFFFF" 
element.strokeWidth = 2
element.movable = true
element.selectable = true
element.fontSize = 12
element.fontWeight = "bold"
```

### **APIs Não Testadas (❓ Precisam Verificação)**

```javascript
// Estas podem não funcionar ou ter sintaxe diferente:
editor.factory.createEllipse()    // ❓ Não confirmado
editor.factory.createPolygon()    // ❓ Alternativa para fatias
editor.factory.createCircle()     // ❓ Para bordas circulares
```

---

## 📋 **ESTRUTURA DE DADOS**

### **Dados Recebidos do Servidor**

```javascript
{
  id: "8aad17cd-8211-4970-ac5e-6fe227ed11dc",
  timestamp: "2025-08-04T20:46:45.957Z",
  
  usuario: {
    nome: "Teste",
    email: "<EMAIL>", 
    hasUserData: true
  },
  
  patrimonio: {
    total: 9000,
    totalFormatted: "R$ 9.000,00",
    alocado: 9000,
    alocadoFormatted: "R$ 9.000,00", 
    restante: 0,
    restanteFormatted: "R$ 0,00",
    percentualAlocado: "100%"
  },
  
  ativos: {
    alocacao: [
      {
        key: "Outros-Poupança",
        category: "Outros", 
        product: "Poupança",
        value: 3200,
        valueFormatted: "R$ 3.200,00",
        percentageFormatted: "36%"
      },
      // ... mais 3 itens similares
    ]
  }
}
```

### **Validação de Dados Implementada**

```javascript
// Verificações automáticas em DataManager.js:
✅ data.usuario existe e tem .nome
✅ data.patrimonio existe e tem .total, .alocado
✅ data.ativos.alocacao é array com length > 0  
✅ Cada item tem .product, .value, .valueFormatted
✅ Soma dos values == patrimonio.alocado
```

---

## 🧪 **SISTEMA DE DEBUG**

### **Logs Implementados**

```javascript
// Níveis de log com prefixos:
🧪 [App] - Logs do componente principal
🔍 [useDGMCanvas] - Logs do hook do canvas  
🥧 [PieChart] - Logs da criação do gráfico
🍰 [PieChart] - Logs das fatias individuais
🏷️ [PieChart] - Logs dos labels
📋 [PieChart] - Logs da legenda  
🔔 [DataManager] - Logs do gerenciador de dados
📝 [DataManager] - Logs de registro de listeners
```

### **Ferramentas de Debug**

```javascript
// Botão de teste manual (temporário):
"🧪 Criar Gráfico Manualmente" 
// - Força criação mesmo sem novos dados
// - Útil para testar mudanças visuais
// - Posição: canto superior direito

// Logs detalhados no console:
// - Cada etapa da criação é logada
// - Try-catch em operações críticas
// - Fallbacks implementados para falhas
```

---

## ⚙️ **CONFIGURAÇÕES CUSTOMIZÁVEIS**

### **PieChart Config**

```javascript
// Valores atuais em PieChart.js constructor:
{
  centerX: 400,           // Centro X do gráfico
  centerY: 350,           // Centro Y do gráfico  
  radius: 120,            // Raio das fatias
  colors: [...],          // Array de cores (10 cores)
}

// Para customizar:
const pieChart = new PieChart(editor, {
  centerX: 500,      // Mover gráfico
  radius: 150,       // Gráfico maior
  colors: ['#...']   // Cores personalizadas
});
```

### **DataManager Config**

```javascript
// Valores atuais em App.jsx:
{
  pollInterval: 2000,     // 2 segundos entre checks
  endpoint: '/api/latest' // Endpoint do servidor
}

// Para customizar:
const dataManager = new DataManager({
  pollInterval: 5000,     // 5 segundos  
  endpoint: '/api/data'   // Endpoint diferente
});
```

---

## 🔄 **PRÓXIMOS PASSOS TÉCNICOS**

### **1. Melhorar Criação das Fatias**

```javascript
// Atual: Retângulos simples
// Meta: Fatias circulares reais usando:
- Mais pontos no arco (atual: 8-32, meta: 64+)
- Algoritmo de bezier curves para suavidade
- Possivelmente usar createPolygon() se disponível
```

### **2. Implementar Animações**

```javascript  
// Usar requestAnimationFrame para:
- Fade-in dos elementos (opacity 0→1)
- Rotação das fatias (startAngle animado)
- Scale dos elementos (transform)
- Delays sequenciais para efeito cascata
```

### **3. Adicionar Interatividade**

```javascript
// Event listeners no DGM.js:
- onClick events nas fatias
- onHover para highlight
- onDrag para reorganizar  
- Tooltips dinâmicos
```

### **4. Sistema de Temas**

```javascript
// Criar ThemeManager.js:
- Paletas de cores predefinidas
- Modo escuro/claro
- Fonts e tamanhos
- Espaçamentos e layouts
```

---

## 🚀 **COMANDOS DE DESENVOLVIMENTO**

### **Iniciar Sistema**

```bash
# Terminal 1 - DGM Canvas (porta 5174)
cd dgm-canvas && npm run dev

# Terminal 2 - App Calc Reino (porta 3000)  
cd app-calc-reino && npm run dev
```

### **Testar Funcionalidades**

```bash
# 1. Abrir navegador: http://localhost:5174/
# 2. Preencher formulário no app-calc-reino
# 3. Ver gráfico aparecer automaticamente
# 4. Clicar "🧪 Criar Gráfico Manualmente" para forçar
```

### **Debug Avançado**

```javascript
// No console do navegador:
window.dataManager = dataManagerRef.current  // Acesso global
window.pieChart = pieChartRef.current        // Para testes

// Comandos úteis:
dataManager.getCurrentData()     // Ver dados atuais
dataManager.checkForData()       // Forçar check  
pieChart.getConfig()             // Ver configurações
editor.fit()                     // Ajustar zoom
```

---

**🎯 Sistema 100% funcional - Pronto para melhorias visuais e UX!**

---

*Documentação técnica criada em: 04/08/2025*  
*Versão: 1.0 - Sistema base implementado*
