# 🔍 G<PERSON>a de Debug - Typebot Integration

## ✅ Logs adicionados para rastrear o fluxo completo

### 📊 **Pontos de Debug configurados:**

#### 1. **WebflowButton - startTypebotFlow**

- `🎯 [WebflowButton] onTypebotCompletion callback triggered`
- `📋 [WebflowButton] Enhanced form data received`
- `💾 [WebflowButton] Sending enhanced data to Supabase...`
- `📊 [WebflowButton] Supabase result`

#### 2. **TypebotIntegration - startTypebotFlow**

- `🚀 [TypebotIntegration] startTypebotFlow called with`
- `📝 [TypebotIntegration] Completion callback registered`
- `📞 [TypebotIntegration] Total callbacks`
- `🔄 [TypebotIntegration] Prefilled variables`
- `🎯 [TypebotIntegration] Opening Typebot popup...`
- `✅ [TypebotIntegration] Typebot popup opened successfully`

#### 3. **TypebotIntegration - Listeners**

- `🔧 [TypebotIntegration] Setting up completion listeners...`
- `📨 [TypebotIntegration] typebotFlowCompleted event received`
- `📬 [TypebotIntegration] postMessage received`
- `🎯 [TypebotIntegration] typebot-completion message detected`

#### 4. **TypebotIntegration - Completion**

- `🤖 [TypebotIntegration] handleTypebotCompletion called with data`
- `📋 [TypebotIntegration] Current form data`
- `📝 [TypebotIntegration] Extracted user info from Typebot`
- `🔄 [TypebotIntegration] Enhanced form data prepared`
- `📞 [TypebotIntegration] Executing X completion callbacks...`
- `🎯 [TypebotIntegration] Calling completion callback...`
- `✅ [TypebotIntegration] Completion callback executed successfully`

#### 5. **WebflowButton - sendToSupabase**

- `💾 [WebflowButton] sendToSupabase called with data`
- `✅ [WebflowButton] Supabase configuration valid`
- `🧮 [WebflowButton] Total alocado calculated`
- `📋 [WebflowButton] Submission data prepared`
- `🚀 [WebflowButton] Inserting into Supabase table`
- `📊 [WebflowButton] Supabase insert result`
- `✅ [WebflowButton] Data successfully inserted into Supabase`

## 🧪 **Como testar o Debug:**

### 1. **Abra o Console do Navegador**

- F12 → Console tab
- Deixe o console aberto durante todo o teste

### 2. **Execute o fluxo completo:**

1. Preencha o formulário
2. Clique em "Enviar"
3. Complete o chat no Typebot
4. **No último bloco do Typebot, certifique-se que tem o código:**

```javascript
if (window.parent) {
  window.parent.postMessage({
    type: 'typebot-completion',
    data: {
      nome: '{{nome}}',
      email: '{{email}}',
      completed: true
    }
  }, '*');
}
```

### 3. **O que deve aparecer no Console:**

**Início do processo:**

```
🚀 [TypebotIntegration] startTypebotFlow called with: {patrimonio: 100000, ...}
📝 [TypebotIntegration] Completion callback registered
📞 [TypebotIntegration] Total callbacks: 1
🎯 [TypebotIntegration] Opening Typebot popup...
✅ [TypebotIntegration] Typebot popup opened successfully
```

**Quando o Typebot for concluído:**

```
📬 [TypebotIntegration] postMessage received: {type: 'typebot-completion', data: {...}}
🎯 [TypebotIntegration] typebot-completion message detected
🤖 [TypebotIntegration] handleTypebotCompletion called with data: {nome: 'João', email: '<EMAIL>', ...}
📞 [TypebotIntegration] Executing 1 completion callbacks...
🎯 [TypebotIntegration] Calling completion callback...
🎯 [WebflowButton] onTypebotCompletion callback triggered
💾 [WebflowButton] Sending enhanced data to Supabase...
💾 [WebflowButton] sendToSupabase called with data: {nome: 'João', email: '<EMAIL>', ...}
📊 [WebflowButton] Supabase insert result: {result: [...], error: null}
✅ [WebflowButton] Data successfully inserted into Supabase
```

## 🚨 **Possíveis problemas identificados:**

### **Se não aparecer no console:**

- `📬 [TypebotIntegration] postMessage received` → **Problema**: Código JavaScript não foi adicionado no Typebot
- `🎯 [TypebotIntegration] typebot-completion message detected` → **Problema**: Formato da mensagem incorreto
- `🎯 [TypebotIntegration] Calling completion callback...` → **Problema**: Callback não foi registrado
- `💾 [WebflowButton] sendToSupabase called` → **Problema**: Callback falhou

### **Soluções rápidas:**

1. **Verifique se o código JavaScript está no último bloco do Typebot**
2. **Certifique-se que as variáveis `{{nome}}` e `{{email}}` existem no Typebot**
3. **Teste manualmente no console:**

   ```javascript
   window.triggerTypebotCompletion({
     nome: 'Teste',
     email: '<EMAIL>',
     completed: true
   });
   ```

## 📝 **Próximos passos:**

1. Execute o teste com logs habilitados
2. Identifique onde o fluxo para
3. Verifique a configuração correspondente
4. Reporte os logs que aparecem/não aparecem
