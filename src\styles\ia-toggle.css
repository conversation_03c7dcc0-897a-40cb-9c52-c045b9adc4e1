/* IA Toggle System CSS - Supporting ONLY Motion.js animations */
/* NO CSS animations - Motion.js handles everything like product-system.js */

/* Minimal base styles only */
.ia-input_wrapper {
  /* Just basic positioning - Motion.js handles all animations */
  position: relative;
}

.ia-button-alocacao {
  /* Basic button styles - Motion.js handles hover effects */
  cursor: pointer;
  position: relative;
}

.close-ia {
  /* Basic close button styles - Motion.js handles hover effects */
  cursor: pointer;
  position: relative;
}

/* Focus states for accessibility */
.ia-button-alocacao:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.close-ia:focus-visible {
  outline: 2px solid #ff4757;
  outline-offset: 2px;
}

/* Touch targets for mobile */
@media screen and (max-width: 767px) {
  .ia-button-alocacao {
    min-height: 44px;
    min-width: 44px;
  }

  .close-ia {
    min-height: 44px;
    min-width: 44px;
  }
}
