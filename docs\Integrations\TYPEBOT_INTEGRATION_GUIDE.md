# 🤖 Typebot Integration Guide

Este guia explica como configurar e usar a integração do Typebot com o Reino Calculator App.

## 📋 Visão Geral

A integração do Typebot permite que o botão com `element-function="send"` inicie uma conversa no Typebot em vez de enviar diretamente para o Supabase. Após o usuário completar a conversa no Typebot, os dados são automaticamente enviados para o Supabase.

**Fluxo:**

1. Usuário preenche o formulário
2. Clica no botão "Enviar"
3. **NOVO**: Inicia conversa no Typebot com dados pré-preenchidos
4. Usuário conversa com o bot
5. Ao finalizar, dados são enviados para o Supabase automaticamente

## 🔧 Configuração

### 1. Configure suas credenciais do Typebot

Edite o arquivo `src/config/typebot.js`:

```javascript
const TYPEBOT_CONFIG = {
  // Substitua pelo seu Public ID do Typebot
  PUBLIC_ID: 'your-typebot-public-id', // ⚠️ ALTERE AQUI
  
  // URL da API (use sua instância ou typebot.io)
  API_BASE_URL: 'https://typebot.io/api/v1',
  
  // Token de autorização (opcional - para bots privados)
  AUTH_TOKEN: null, // ou 'Bearer your-token'
  
  // ... outras configurações
};
```

### 2. Como encontrar seu Public ID

1. Vá para o seu Typebot
2. Clique em "Share"
3. Copie o ID da URL: `https://typebot.io/your-public-id-here`
4. Cole no `PUBLIC_ID` da configuração

### 3. Configuração do Typebot

#### 3.1. No Dashboard do Typebot

1. **Acesse o Typebot.io** e faça login na sua conta
2. **Crie um novo typebot** ou acesse um existente

#### 3.2. Configuração de Variáveis de Entrada

Configure as seguintes variáveis no seu Typebot:

**Variáveis de Dados do Formulário** (opcionais):

- `patrimonio`: Valor do patrimônio informado
- `ativos`: Lista de ativos selecionados
- `totalAlocado`: Total já alocado pelo usuário

**Variáveis de Saída** (obrigatórias):

- `nome`: Nome completo do usuário
- `email`: E-mail do usuário

#### 3.3. Como Configurar Variáveis no Typebot

1. **No editor do Typebot**:
   - Adicione um bloco de "Text input" para capturar o nome
   - Configure a variável como `nome`
   - Adicione um bloco de "Email input" para capturar o e-mail  
   - Configure a variável como `email`

2. **Exemplo de configuração**:

   ```
   Bloco 1: "Olá! Para começar, qual é o seu nome?"
   → Salvar resposta em: nome
   
   Bloco 2: "Ótimo, {{nome}}! Qual é o seu e-mail?"
   → Salvar resposta em: email
   → Validação: formato de e-mail
   
   Bloco 3: "Perfeito! Vamos falar sobre seus investimentos..."
   ```

#### 3.4. Uso de Variáveis de Entrada

Para usar os dados do formulário no Typebot:

- Use `{{patrimonio}}` para mostrar o valor do patrimônio
- Use `{{ativos}}` para referenciar os ativos selecionados
- Use `{{totalAlocado}}` para mostrar o total alocado

#### 3.5. Configuração de Webhooks

Para detectar quando o usuário completa o fluxo do Typebot:

#### 3.6. Exemplo Prático de Configuração no Typebot

**Passo 1: Criar variáveis de entrada**

1. No editor do Typebot, vá em "Variables" no menu lateral
2. Crie as seguintes variáveis:
   - `patrimonio` (Number)
   - `ativos` (Text)
   - `totalAlocado` (Number)

**Passo 2: Configurar blocos para capturar nome e e-mail**

```
Bloco Inicial:
"Olá! Vi que você preencheu nosso formulário com patrimônio de R$ {{patrimonio}}.
Para continuar, preciso de algumas informações."

Bloco 1 - Nome:
"Qual é o seu nome completo?"
→ Tipo: Text Input
→ Salvar em variável: nome
→ Obrigatório: Sim

Bloco 2 - E-mail:
"Perfeito, {{nome}}! Qual é o seu melhor e-mail?"
→ Tipo: Email Input  
→ Salvar em variável: email
→ Obrigatório: Sim
→ Validação: formato de e-mail

Bloco 3 - Confirmação:
"Excelente! Com patrimônio de R$ {{patrimonio}} e interesse em {{ativos}}, 
vamos conversar sobre as melhores opções para você."

Bloco Final - Webhook:
→ Tipo: Webhook/HTTP Request
→ URL: Seu endpoint de completion
→ Método: POST
→ Body: {
  "nome": "{{nome}}",
  "email": "{{email}}",
  "patrimonio": "{{patrimonio}}",
  "completed": true
}
```

**Passo 3: Configurar Webhook de Completion**

1. No último bloco do seu fluxo, adicione uma ação "HTTP Request"
2. Configure:
   - **URL**: `https://seu-site.com/api/typebot-completion` (ou use postMessage)
   - **Method**: POST
   - **Headers**: `{"Content-Type": "application/json"}`
   - **Body**:

   ```json
   {
     "nome": "{{nome}}",
     "email": "{{email}}",
     "completed": true,
     "sessionId": "{{session_id}}"
   }
   ```

**Alternativa - Usar postMessage (mais simples)**:
No último bloco, adicione um "Code" block com:

```javascript
window.parent.postMessage({
  type: 'typebot-completion',
  data: {
    nome: '{{nome}}',
    email: '{{email}}',
    completed: true
  }
}, '*');
```

### 4. Configuração do Banco de Dados (Supabase)

Execute o seguinte SQL no Supabase SQL Editor para adicionar as colunas de nome e e-mail:

```sql
-- Adicionar colunas de nome e e-mail à tabela existente
ALTER TABLE patrimonio_submissions 
ADD COLUMN IF NOT EXISTS nome TEXT,
ADD COLUMN IF NOT EXISTS email TEXT;

-- Adiciona comentários às colunas para documentação
COMMENT ON COLUMN patrimonio_submissions.nome IS 'Nome do usuário coletado via Typebot';
COMMENT ON COLUMN patrimonio_submissions.email IS 'E-mail do usuário coletado via Typebot';

-- Adiciona índices para facilitar buscas
CREATE INDEX IF NOT EXISTS idx_patrimonio_submissions_email 
ON patrimonio_submissions(email);

CREATE INDEX IF NOT EXISTS idx_patrimonio_submissions_nome 
ON patrimonio_submissions(nome);
```

### 5. Mapeamento de Variáveis

Edite `VARIABLE_MAPPING` no mesmo arquivo para mapear seus campos do formulário para variáveis do Typebot:

```javascript
const VARIABLE_MAPPING = {
  // Seus campos -> Variáveis do Typebot
  patrimonio: 'Patrimonio',
  email: 'Email',
  nome: 'Nome',
  telefone: 'Telefone',
  // ... adicione mais conforme necessário
};
```

## 🚀 Como Usar

### Automático

A integração funciona automaticamente uma vez configurada:

- O botão `[element-function="send"]` detecta se o Typebot está disponível
- Se sim, inicia o fluxo do Typebot
- Se não, usa o método direto para Supabase (fallback)

### Manual via JavaScript

```javascript
// Verificar status
console.log(ReinoCalculator.typebot.status());

// Desabilitar Typebot temporariamente
ReinoCalculator.typebot.disable();

// Reabilitar
ReinoCalculator.typebot.enable();

// Iniciar manualmente
const formData = { patrimonio: 50000, email: '<EMAIL>' };
ReinoCalculator.typebot.integration.startTypebotFlow(formData, (data) => {
  console.log('Typebot completed:', data);
});
```

## 🎯 Configuração do Typebot (lado do bot)

### 1. Variáveis Pré-preenchidas

No seu Typebot, configure as seguintes variáveis:

- `Patrimonio` - Valor do patrimônio
- `Email` - Email do usuário
- `Nome` - Nome do usuário
- `SelectedAssets` - Lista de ativos selecionados
- `AllocationData` - Dados de alocação
- `SessionId` - ID da sessão

### 2. Webhook de Conclusão

Para detectar quando o usuário terminou a conversa, adicione um bloco "HTTP Request" no final do seu flow:

```javascript
// URL: Seu endpoint de webhook
POST https://seu-dominio.com/api/typebot-completion

// Body:
{
  "type": "completion",
  "sessionId": "{{SessionId}}",
  "userData": {
    "patrimonio": "{{Patrimonio}}",
    "email": "{{Email}}",
    // ... outros campos
  }
}
```

### 3. Script de Conclusão (Alternativo)

Ou adicione um bloco "Script" no final:

```javascript
// Script para executar no navegador
window.parent.postMessage({
  type: 'typebot-completion',
  data: {
    sessionId: '{{SessionId}}',
    patrimonio: '{{Patrimonio}}',
    email: '{{Email}}',
    // ... outros dados coletados
  }
}, '*');
```

## 🐛 Debug e Troubleshooting

### Ativar Debug Mode

Adicione `?debug=true` na URL ou configure:

```javascript
// No console do navegador
ReinoCalculator.typebot.integration.config.DEBUG = true;
```

### Comandos de Debug

```javascript
// Status da integração
console.log(ReinoCalculator.typebot.status());

// Status completo do sistema
console.log(ReinoCalculator.getSystemStatus());

// Verificar se Typebot está ativo
console.log(ReinoCalculator.typebot.integration.isTypebotActive);
```

### Problemas Comuns

**1. "Typebot not available, using direct Supabase submission"**

- Verifique se o `PUBLIC_ID` está correto
- Confirme se a API do Typebot está respondendo
- Verifique a rede/CORS

**2. "Failed to start Typebot chat"**

- Verifique o `AUTH_TOKEN` se necessário
- Confirme se o bot está publicado
- Teste a URL da API

**3. Dados não chegam no Supabase após o Typebot**

- Verifique se o webhook de conclusão está configurado
- Confirme se o script de conclusão está executando
- Teste manualmente: `window.triggerTypebotCompletion({data: 'test'})`

## 📊 Monitoramento

### Events Disponíveis

```javascript
// Quando Typebot inicia
document.addEventListener('typebotStarted', (event) => {
  console.log('Typebot started:', event.detail);
});

// Quando Typebot completa
document.addEventListener('typebotCompleted', (event) => {
  console.log('Typebot completed:', event.detail);
});
```

### Métricas

```javascript
// Obter métricas de uso
const status = ReinoCalculator.getSystemStatus();
console.log('Typebot initialized:', status.typebotIntegration.initialized);
console.log('Webflow button ready:', status.webflowButton.initialized);
```

## 🔄 Fallback Behavior

Se o Typebot não estiver disponível, o sistema automaticamente:

1. Mostra uma mensagem indicando o fallback
2. Envia dados diretamente para o Supabase
3. Exibe mensagem de sucesso normal

Isso garante que o formulário sempre funcione, mesmo se houver problemas com o Typebot.

## 🎨 Customização UI

### Embed Container

O Typebot pode ser exibido em um container embarcado:

```javascript
// Configurar container no typebot.js
EMBED_CONFIG: {
  containerId: 'typebot-container',
  theme: {
    button: { backgroundColor: '#0042DA' },
    chatWindow: { maxWidth: '400px' }
  }
}
```

### Mensagens Customizadas

Personalize as mensagens no `webflow-button-integration.js`:

```javascript
this.showSuccessMessage(
  'Conversa iniciada! Continue no chat para finalizar.'
);
```

## 📝 Exemplo Completo

Aqui está um exemplo de configuração completa:

```javascript
// src/config/typebot.js
const TYPEBOT_CONFIG = {
  PUBLIC_ID: 'reino-calculator-bot-abc123',
  API_BASE_URL: 'https://typebot.io/api/v1',
  AUTH_TOKEN: null,
  DEBUG: true,
};

const VARIABLE_MAPPING = {
  patrimonio: 'Patrimonio',
  email: 'Email',
  selectedAssets: 'SelectedAssets',
};
```

**No Typebot:**

1. Configure variáveis: `Patrimonio`, `Email`, `SelectedAssets`
2. Adicione script de conclusão no final
3. Publique o bot

**Resultado:**

- Usuário preenche formulário
- Clica "Enviar" → Abre Typebot
- Conversa com bot
- Ao final → Dados salvos no Supabase automaticamente

## 📞 Suporte

Para dúvidas sobre a integração:

1. Verifique os logs do console (debug mode)
2. Teste com dados simples primeiro
3. Confirme se tanto Typebot quanto Supabase estão funcionando separadamente
4. Use os comandos de debug para identificar onde está o problema

---

**🎉 Pronto!** Com essa configuração, você terá uma experiência de conversação fluida que automaticamente salva os dados no final.
