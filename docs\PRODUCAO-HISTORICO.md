# 🚀 Guia de Produção - Sistema de Histórico DGM Canvas

## 📋 Visão Geral

Este documento detalha os passos para colocar o sistema de histórico integrado entre **DGM Canvas** e **app-calc-reino** em produção.

### 🏗️ Arquitetura Atual

```
┌─────────────────┐    API HTTP    ┌──────────────────┐    SQL    ┌─────────────┐
│   DGM Canvas    │◄──────────────►│  app-calc-reino  │◄─────────►│  Supabase   │
│   (Frontend)    │                │   (Backend)      │           │ (Database)  │
└─────────────────┘                └──────────────────┘           └─────────────┘
```

## 🎯 Status Atual

### ✅ Implementado e Testado

- [x] **API de Histórico** - Endpoints funcionais em `history-endpoints.js`
- [x] **DataManager Estendido** - Métodos de histórico implementados
- [x] **Componente React** - `HistoryControlPanel.jsx` funcional
- [x] **Servidor de Desenvolvimento** - Testado com dados mock
- [x] **Integração Frontend** - DGM Canvas conecta com API

### 🔄 Próximos Passos para Produção

## 1. 🛠️ Configuração do Backend (app-calc-reino)

### 1.1 Instalar Dependências

```bash
cd "c:\Users\<USER>\Desktop\Integração Reino\app-calc-reino"
npm install express cors
```

### 1.2 Configurar Servidor de Produção

Crie o arquivo `src/history-server-prod.js`:

```javascript
/**
 * Production History API Server
 * Integrates with existing app-calc-reino infrastructure
 */

import express from 'express';
import cors from 'cors';
import { setupHistoryAPI } from './api/history-server-v2.js';

const app = express();
const PORT = process.env.HISTORY_API_PORT || 3001;

// Middleware
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://seu-dominio.com', 'https://dgm-canvas.seu-dominio.com']
    : ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'history-api'
  });
});

// Setup history API routes
setupHistoryAPI(app);

// Error handling
app.use((err, req, res, next) => {
  console.error('❌ [HistoryAPI] Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 History API Server running on port ${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📡 CORS enabled for: ${app.get('env') === 'production' ? 'production domains' : 'localhost'}`);
});

export default app;
```

### 1.3 Configurar Variáveis de Ambiente

Adicione ao `.env` do app-calc-reino:

```env
# History API Configuration
HISTORY_API_PORT=3001
HISTORY_API_ENABLED=true

# CORS Configuration
CORS_ORIGINS=https://dgm-canvas.seu-dominio.com,https://seu-dominio.com

# Database Connection (já existe)
SUPABASE_URL=sua_url
SUPABASE_ANON_KEY=sua_chave
```

### 1.4 Atualizar package.json

Adicione scripts ao `package.json`:

```json
{
  "scripts": {
    "history:dev": "node src/history-server-prod.js",
    "history:prod": "NODE_ENV=production node src/history-server-prod.js",
    "history:pm2": "pm2 start src/history-server-prod.js --name history-api"
  }
}
```

## 2. 🌐 Configuração do Frontend (DGM Canvas)

### 2.1 Configurar Ambiente de Produção

Crie `src/config/environment.js`:

```javascript
/**
 * Environment Configuration for DGM Canvas
 */

export const ENV_CONFIG = {
  development: {
    historyApiUrl: 'http://localhost:3001/api/history',
    pollInterval: 2000,
    debugMode: true
  },
  
  production: {
    historyApiUrl: '/api/history', // Proxy via nginx
    pollInterval: 5000,
    debugMode: false
  }
};

export const getCurrentConfig = () => {
  const env = import.meta.env.MODE || 'development';
  return ENV_CONFIG[env] || ENV_CONFIG.development;
};
```

### 2.2 Atualizar DataManager para Produção

Modifique `src/utils/DataManager.js`:

```javascript
import { getCurrentConfig } from '../config/environment.js';

export class DataManager {
  constructor(config = {}) {
    const envConfig = getCurrentConfig();
    
    this.config = {
      pollInterval: config.pollInterval || envConfig.pollInterval,
      endpoint: config.endpoint || "/api/latest",
      historyEndpoint: config.historyEndpoint || envConfig.historyApiUrl,
      debugMode: envConfig.debugMode,
      ...config,
    };
    // ... resto do código
  }
}
```

### 2.3 Build para Produção

```bash
cd "c:\Users\<USER>\Desktop\Zero\dgm-canvas"
npm run build
```

## 3. 🚀 Deploy e Infraestrutura

### 3.1 Opção A: Deploy com PM2 (Recomendado)

#### Instalar PM2

```bash
npm install -g pm2
```

#### Arquivo de Configuração PM2

Crie `ecosystem.config.js` no app-calc-reino:

```javascript
module.exports = {
  apps: [
    {
      name: 'app-calc-reino-main',
      script: 'src/app.js', // ou seu arquivo principal atual
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      }
    },
    {
      name: 'history-api',
      script: 'src/history-server-prod.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      env: {
        NODE_ENV: 'production',
        HISTORY_API_PORT: 3001
      }
    }
  ]
};
```

#### Comandos de Deploy

```bash
# Iniciar aplicações
pm2 start ecosystem.config.js

# Monitorar
pm2 monit

# Logs
pm2 logs history-api

# Restart
pm2 restart history-api
```

### 3.2 Opção B: Docker (Avançado)

#### Dockerfile para app-calc-reino

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000 3001

CMD ["node", "src/history-server-prod.js"]
```

#### docker-compose.yml

```yaml
version: '3.8'
services:
  app-calc-reino:
    build: .
    ports:
      - "3000:3000"
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    restart: unless-stopped

  dgm-canvas:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./dgm-canvas/dist:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app-calc-reino
```

## 4. 🔧 Configuração do Servidor Web

### 4.1 Nginx Configuration

Crie `nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream history_api {
        server localhost:3001;
    }

    upstream main_app {
        server localhost:3000;
    }

    # DGM Canvas (Frontend)
    server {
        listen 80;
        server_name dgm-canvas.seu-dominio.com;

        root /var/www/dgm-canvas;
        index index.html;

        # Static assets
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "public, max-age=31536000";
        }

        # Proxy para History API
        location /api/history {
            proxy_pass http://history_api;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
    }

    # App Calc Reino (Backend)
    server {
        listen 80;
        server_name api.seu-dominio.com;

        location / {
            proxy_pass http://main_app;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # History API
        location /api/history {
            proxy_pass http://history_api;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## 5. 🔐 Segurança e Performance

### 5.1 Rate Limiting

Adicione ao `history-server-prod.js`:

```javascript
import rateLimit from 'express-rate-limit';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // máximo 100 requests por IP
  message: {
    success: false,
    error: 'Too many requests, please try again later.'
  }
});

app.use('/api/history', limiter);
```

### 5.2 Autenticação (Opcional)

Se necessário, adicione middleware de auth:

```javascript
const authMiddleware = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey || apiKey !== process.env.HISTORY_API_KEY) {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized'
    });
  }
  
  next();
};

app.use('/api/history', authMiddleware);
```

### 5.3 Caching

Adicione Redis para cache (opcional):

```javascript
import redis from 'redis';
const client = redis.createClient();

const cacheMiddleware = (duration = 300) => {
  return async (req, res, next) => {
    const key = req.originalUrl;
    
    try {
      const cached = await client.get(key);
      if (cached) {
        return res.json(JSON.parse(cached));
      }
    } catch (err) {
      console.error('Cache error:', err);
    }
    
    res.sendResponse = res.json;
    res.json = (body) => {
      client.setex(key, duration, JSON.stringify(body));
      res.sendResponse(body);
    };
    
    next();
  };
};
```

## 6. 📊 Monitoramento

### 6.1 Health Checks

Adicione endpoints de saúde:

```javascript
// Health check detalhado
app.get('/api/history/health', async (req, res) => {
  try {
    // Testar conexão com Supabase
    const { data, error } = await supabase
      .from('calculator_submissions')
      .select('count')
      .limit(1);
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: error ? 'error' : 'ok',
        api: 'ok'
      },
      version: '1.0.0'
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});
```

### 6.2 Logs Estruturados

```javascript
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/history-api.log' }),
    new winston.transports.Console()
  ]
});
```

## 7. 🧪 Testes de Produção

### 7.1 Checklist Pré-Deploy

- [ ] Variáveis de ambiente configuradas
- [ ] Conexão com Supabase testada
- [ ] Build do frontend gerado
- [ ] Nginx configurado
- [ ] SSL/TLS configurado (se necessário)
- [ ] Rate limiting ativo
- [ ] Logs funcionando
- [ ] Health checks respondendo

### 7.2 Scripts de Teste

Crie `test-production.js`:

```javascript
/**
 * Production Tests for History API
 */

const API_BASE = 'https://seu-dominio.com/api/history';

const tests = [
  {
    name: 'Health Check',
    url: `${API_BASE}/health`,
    expected: { status: 'healthy' }
  },
  {
    name: 'Recent Submissions',
    url: `${API_BASE}/recent?limit=5`,
    expected: { success: true }
  },
  {
    name: 'Statistics',
    url: `${API_BASE}/stats`,
    expected: { success: true }
  }
];

async function runTests() {
  for (const test of tests) {
    try {
      console.log(`🧪 Testing: ${test.name}`);
      const response = await fetch(test.url);
      const data = await response.json();
      
      if (response.ok && data.success !== false) {
        console.log(`✅ ${test.name}: PASSED`);
      } else {
        console.log(`❌ ${test.name}: FAILED`, data);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ERROR`, error.message);
    }
  }
}

runTests();
```

## 8. 📝 Manutenção

### 8.1 Backup da Base de Dados

```sql
-- Backup da tabela de submissões
COPY (
  SELECT * FROM calculator_submissions 
  WHERE created_at >= NOW() - INTERVAL '30 days'
) TO '/backup/submissions.csv' WITH CSV HEADER;
```

### 8.2 Limpeza de Dados Antigos

```sql
-- Manter apenas últimos 90 dias
DELETE FROM calculator_submissions 
WHERE created_at < NOW() - INTERVAL '90 days';
```

### 8.3 Monitoramento de Performance

```javascript
// Middleware para medir performance
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info({
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`
    });
  });
  
  next();
});
```

## 🎉 Conclusão

Após seguir todos estes passos, você terá:

1. ✅ **API de Histórico** rodando em produção
2. ✅ **DGM Canvas** conectado ao histórico
3. ✅ **Monitoramento** e logs funcionais
4. ✅ **Segurança** implementada
5. ✅ **Performance** otimizada

### 📞 Suporte

Em caso de problemas:

1. Verificar logs: `pm2 logs history-api`
2. Health check: `curl https://seu-dominio.com/api/history/health`
3. Restart: `pm2 restart history-api`

---

**🔗 Documentação criada em:** ${new Date().toISOString()}  
**🚀 Status:** Pronto para deploy em produção
