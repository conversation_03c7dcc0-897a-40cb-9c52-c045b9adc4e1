/* Asset Selection Filter Styles */

.asset-checkbox-container {
  display: inline-flex;
  align-items: center;
  margin-right: 0.5rem;
  vertical-align: middle;
}

.asset-checkbox {
  appearance: none;
  width: 1.2rem;
  height: 1.2rem;
  border: 2px solid #ccc;
  border-radius: 0.25rem;
  margin: 0;
  margin-right: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  background: white;
}

.asset-checkbox:hover {
  border-color: #000;
}

.asset-checkbox:checked {
  background-color: #000;
  border-color: #000;
}

.asset-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
}

.asset-checkbox-label {
  cursor: pointer;
  margin: 0;
}

/* Selected asset styling */
.selected-asset {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  transform: scale(1.02);
  transition: all 0.2s ease;
}

.selected-asset:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

/* Dropdown items styling */
.ativo-item-subcategory .asset-checkbox-container {
  margin-right: 0.75rem;
}

.ativo-item-subcategory.selected-asset {
  background-color: rgba(0, 0, 0, 0.1);
  color: white;
}

/* Individual items styling */
.ativos_item .asset-checkbox-container {
  order: 2;
  margin-left: 0.5rem;
  margin-right: 0;
}

.ativos_item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.ativos_item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

/* Counter styling enhancement */
.counter_ativos {
  font-weight: bold;
  color: #000;
}

/* Section 3 filtered states */
.asset-filtered-out {
  opacity: 0;
  transform: scale(0.95);
  transition: all 0.3s ease;
  pointer-events: none;
}

.asset-filtered-in {
  opacity: 1;
  transform: scale(1);
  transition: all 0.3s ease;
}

/* Clear button enhancement */
.ativos_clean-button {
  transition: all 0.2s ease;
}

.ativos_clean-button:hover {
  transform: scale(1.05);
  background-color: rgba(255, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .asset-checkbox {
    width: 1rem;
    height: 1rem;
  }

  .asset-checkbox:checked::after {
    font-size: 0.7rem;
  }

  .asset-checkbox-container {
    margin-right: 0.25rem;
  }
}

/* Animation for selection */
@keyframes selectAsset {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1.02);
  }
}

.selected-asset {
  animation: selectAsset 0.3s ease;
}

/* Improve visual hierarchy */
.ativos_item.dropdown .selected-asset {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ativos_item:not(.dropdown).selected-asset {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
