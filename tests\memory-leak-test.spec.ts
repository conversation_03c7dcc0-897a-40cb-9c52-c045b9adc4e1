import { expect, test } from '@playwright/test';

test.describe('Memory Leak Investigation', () => {
  test('should identify memory issues when interacting with main input', async ({ page }) => {
    // Monitor console errors and memory usage
    const errors: string[] = [];
    const warnings: string[] = [];

    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
      if (msg.type() === 'warning') {
        warnings.push(msg.text());
      }
    });

    page.on('pageerror', (error) => {
      errors.push(`Page Error: ${error.message}`);
    });

    // Navigate to the page
    await page.goto(
      'file:///C:/Users/<USER>/Desktop/Integração Reino/app-calc-reino/Modelo - Webflow/index.html'
    );

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Find the main input
    const mainInput = page.locator('[is-main="true"]');
    await expect(mainInput).toBeVisible({ timeout: 10000 });

    // Monitor JavaScript heap before interaction
    const heapBefore = await page.evaluate(() => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize;
      }
      return 0;
    });

    console.log('Heap before interaction:', heapBefore);

    // Test 1: Type in the main input multiple times
    console.log('Testing main input typing...');
    for (let i = 0; i < 5; i++) {
      await mainInput.clear();
      await mainInput.fill('10000');
      await page.waitForTimeout(500);

      // Check if any errors occurred
      if (errors.length > 0) {
        console.log('Errors during typing:', errors);
        break;
      }
    }

    // Test 2: Click increase/decrease buttons
    console.log('Testing currency control buttons...');
    const increaseBtn = page.locator('[currency-control="increase"]').first();
    const decreaseBtn = page.locator('[currency-control="decrease"]').first();

    if (await increaseBtn.isVisible()) {
      for (let i = 0; i < 5; i++) {
        await increaseBtn.click();
        await page.waitForTimeout(200);

        if (errors.length > 0) {
          console.log('Errors during increase button:', errors);
          break;
        }
      }
    }

    if (await decreaseBtn.isVisible()) {
      for (let i = 0; i < 5; i++) {
        await decreaseBtn.click();
        await page.waitForTimeout(200);

        if (errors.length > 0) {
          console.log('Errors during decrease button:', errors);
          break;
        }
      }
    }

    // Monitor event listeners count
    const eventListenersCount = await page.evaluate(() => {
      const allElements = document.querySelectorAll('*');
      let totalListeners = 0;

      allElements.forEach((element) => {
        const listeners = (element as any)._eventListeners;
        if (listeners) {
          totalListeners += Object.keys(listeners).length;
        }
      });

      return totalListeners;
    });

    console.log('Event listeners count:', eventListenersCount);

    // Check for duplicate event listeners
    const duplicateListeners = await page.evaluate(() => {
      const mainInput = document.querySelector('[is-main="true"]');
      if (!mainInput) return 0;

      // Count how many input event listeners are attached
      const inputEvents = (mainInput as any)._inputListeners || [];
      return inputEvents.length;
    });

    console.log('Duplicate listeners on main input:', duplicateListeners);

    // Monitor heap after interaction
    const heapAfter = await page.evaluate(() => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize;
      }
      return 0;
    });

    console.log('Heap after interaction:', heapAfter);
    console.log('Heap difference:', heapAfter - heapBefore);

    // Test modular version
    console.log('\n=== Testing Modular Version ===');

    await page.goto('http://localhost:3000'); // Assuming you have a local server
    await page.waitForLoadState('networkidle');

    const modularMainInput = page.locator('[is-main="true"]');
    if (await modularMainInput.isVisible()) {
      console.log('Testing modular version...');

      for (let i = 0; i < 5; i++) {
        await modularMainInput.clear();
        await modularMainInput.fill('10000');
        await page.waitForTimeout(500);

        if (errors.length > 0) {
          console.log('Errors in modular version:', errors);
          break;
        }
      }
    }

    // Report findings
    console.log('\n=== Memory Leak Investigation Results ===');
    console.log('Errors found:', errors);
    console.log('Warnings found:', warnings);

    // Fail test if memory issues are detected
    if (errors.some((error) => error.includes('memory') || error.includes('Maximum call stack'))) {
      throw new Error('Memory leak or stack overflow detected: ' + errors.join(', '));
    }
  });

  test('should detect infinite loops in event handlers', async ({ page }) => {
    // Set up performance monitoring
    await page.goto(
      'file:///C:/Users/<USER>/Desktop/Integração Reino/app-calc-reino/Modelo - Webflow/index.html'
    );

    // Inject monitoring script
    await page.addScriptTag({
      content: `
        let eventCallCount = 0;
        let maxCalls = 100;
        
        // Override addEventListener to monitor calls
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
          if (type === 'input' || type === 'change') {
            const wrappedListener = function(event) {
              eventCallCount++;
              if (eventCallCount > maxCalls) {
                console.error('Potential infinite loop detected in ' + type + ' event');
                throw new Error('Infinite loop detected in event handler');
              }
              
              // Reset counter after a delay if not infinite
              setTimeout(() => {
                if (eventCallCount > 0) eventCallCount = Math.max(0, eventCallCount - 1);
              }, 100);
              
              return listener.call(this, event);
            };
            return originalAddEventListener.call(this, type, wrappedListener, options);
          }
          return originalAddEventListener.call(this, type, listener, options);
        };
        
        window.getEventCallCount = () => eventCallCount;
      `,
    });

    const mainInput = page.locator('[is-main="true"]');
    await expect(mainInput).toBeVisible();

    // Try to trigger the issue
    try {
      await mainInput.fill('5000');
      await page.waitForTimeout(2000);

      const callCount = await page.evaluate(() => window.getEventCallCount());
      console.log('Event call count:', callCount);

      if (callCount > 50) {
        console.log('High event call count detected, possible infinite loop');
      }
    } catch (error) {
      console.log('Error caught:', error.message);
      if (error.message.includes('Infinite loop')) {
        throw error;
      }
    }
  });
});
