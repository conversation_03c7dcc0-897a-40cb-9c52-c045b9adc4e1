# ✅ Checklist - Configuração Typebot Reino

## 🎯 Configurações já feitas

### ✅ Código (src/config/typebot.js)

- **PUBLIC_ID**: `relatorio-reino`
- **API_BASE_URL**: `https://typebot.co/api/v1`
- **AUTH_TOKEN**: `Bearer Dza0r0rRB4rzjSeGiKlawQK8`
- **Mapeamento de variáveis**: atualizado

---

## ⏳ O que ainda falta fazer

### 1. 🗄️ **Banco de Dad<PERSON> (Supabase)**

Execute no Supabase SQL Editor:

```sql
-- <PERSON><PERSON><PERSON> as novas colunas
ALTER TABLE calculator_submissions 
ADD COLUMN IF NOT EXISTS nome TEXT,
ADD COLUMN IF NOT EXISTS email TEXT;

-- Ad<PERSON><PERSON> comentários às colunas para documentação
COMMENT ON COLUMN calculator_submissions.nome IS 'Nome do usuário coletado via Typebot';
COMMENT ON COLUMN calculator_submissions.email IS 'E-mail do usuário coletado via Typebot';

-- Adiciona índices para facilitar buscas
CREATE INDEX IF NOT EXISTS idx_calculator_submissions_email 
ON calculator_submissions(email);

CREATE INDEX IF NOT EXISTS idx_calculator_submissions_nome 
ON calculator_submissions(nome);
```

### 2. 🤖 **No Typebot (<https://typebot.co/relatorio-reino>)**

#### A. Criar variáveis no Typebot

1. Acesse seu typebot em <https://typebot.co/relatorio-reino>
2. Vá em "Variables" no menu lateral
3. **Crie estas variáveis** (exatamente com estes nomes):
   - `patrimonio` (Number) - receberá o valor do formulário
   - `ativos` (Text) - receberá os ativos selecionados  
   - `nome` (Text) - para capturar o nome do usuário
   - `email` (Text) - para capturar o e-mail do usuário

#### B. Configurar o fluxo

1. **Bloco inicial** (use os dados pré-preenchidos):

   ```
   "Olá! Vi que você tem patrimônio de R$ {{patrimonio}} 
   e interesse em {{ativos}}.
   
   Para continuar, preciso de algumas informações."
   ```

2. **Bloco - Capturar Nome**:
   - Tipo: Text Input
   - Pergunta: "Qual é o seu nome completo?"
   - Salvar em variável: `nome`
   - Marcar como obrigatório

3. **Bloco - Capturar E-mail**:
   - Tipo: Email Input
   - Pergunta: "Perfeito, {{nome}}! Qual é o seu melhor e-mail?"
   - Salvar em variável: `email`
   - Marcar como obrigatório

4. **Bloco final - Conclusão**:
   - Mensagem: "Obrigado, {{nome}}! Em breve entraremos em contato via {{email}}."

#### C. **IMPORTANTE - Bloco de Completion**

No último bloco, adicione um **Code Block** com este JavaScript:

```javascript
// Notifica o sistema que o typebot foi concluído
if (window.parent) {
  window.parent.postMessage({
    type: 'typebot-completion',
    data: {
      nome: '{{nome}}',
      email: '{{email}}',
      completed: true
    }
  }, '*');
}

// Para embed direto
if (window.triggerTypebotCompletion) {
  window.triggerTypebotCompletion({
    nome: '{{nome}}',
    email: '{{email}}',
    completed: true
  });
}
```

### 3. 🧪 **Teste completo**

1. Execute o SQL no Supabase ✅
2. Configure as variáveis no Typebot ✅  
3. Configure o fluxo no Typebot ✅
4. Adicione o código de completion ✅
5. **Teste**: preencha o formulário → clique "Enviar" → complete o typebot
6. **Verifique**: dados devem aparecer no Supabase com nome e e-mail

---

## 🚨 Ponto crítico

**O código de completion no último bloco do Typebot é ESSENCIAL** - sem ele, os dados não vão para o Supabase após o typebot.

---

## 🔍 Debug

- Use `?debug=true` na URL para ver logs detalhados
- Console do navegador mostrará o progresso da integração
