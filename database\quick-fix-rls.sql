-- SOLUÇÃO RÁPIDA: Execute apenas este SQL para corrigir o erro RLS
-- No painel do Supabase, vá em SQL Editor e execute:

-- Ativar Row Level Security se não estiver ativado
ALTER TABLE calculator_submissions ENABLE ROW LEVEL SECURITY;

-- Remover políticas existentes se houver
DROP POLICY IF EXISTS "Allow public inserts" ON calculator_submissions;
DROP POLICY IF EXISTS "Allow admin reads" ON calculator_submissions;

-- Criar política para permitir inserções públicas
CREATE POLICY "Allow public inserts" ON calculator_submissions
  FOR INSERT WITH CHECK (true);

-- OPCIONAL: Se você quiser também permitir leitura pública dos dados
-- Descomente a linha abaixo se quiser que qualquer um possa ver os dados
-- CREATE POLICY "Allow public reads" ON calculator_submissions
--   FOR SELECT USING (true);

-- Verificar se as políticas foram criadas corretamente
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'calculator_submissions';
