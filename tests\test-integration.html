<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Teste DGM Canvas Integration</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .test-button {
        background: #4caf50;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        margin: 10px 5px;
      }
      .test-button:hover {
        background: #45a049;
      }
      .status {
        margin: 20px 0;
        padding: 15px;
        border-radius: 5px;
        border-left: 4px solid #2196f3;
        background: #e3f2fd;
      }
      .success {
        border-left-color: #4caf50;
        background: #e8f5e8;
      }
      .error {
        border-left-color: #f44336;
        background: #ffebee;
      }
      .code-block {
        background: #f4f4f4;
        padding: 15px;
        border-radius: 5px;
        font-family: "Courier New", monospace;
        font-size: 14px;
        overflow-x: auto;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎯 Teste de Integração DGM Canvas</h1>
      <p>
        Use este arquivo para testar o envio de dados para o DGM Canvas em
        localhost.
      </p>

      <h2>📡 Configuração</h2>
      <div class="code-block">
        DGM Canvas: http://localhost:5173<br />
        API Endpoint: http://localhost:5173/api/data<br />
        Health Check: http://localhost:5173/api/health
      </div>

      <h2>🧪 Testes</h2>
      <button class="test-button" onclick="testHealthCheck()">
        ✅ Health Check
      </button>
      <button class="test-button" onclick="sendTestData()">
        📊 Enviar Dados de Teste
      </button>
      <button class="test-button" onclick="sendRealisticData()">
        💰 Enviar Dados Realistas
      </button>
      <button class="test-button" onclick="checkLatestData()">
        📋 Ver Últimos Dados
      </button>

      <div id="status" class="status">
        Pronto para testar. Certifique-se de que o DGM Canvas está rodando em
        localhost:5173
      </div>

      <div id="results"></div>
    </div>

    <script>
      const API_BASE = "http://localhost:5173/api";

      function updateStatus(message, type = "info") {
        const statusDiv = document.getElementById("status");
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
      }

      function displayResults(title, data) {
        const resultsDiv = document.getElementById("results");
        resultsDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="code-block">${JSON.stringify(data, null, 2)}</div>
            `;
      }

      async function testHealthCheck() {
        updateStatus("🔍 Testando conexão...", "info");

        try {
          const response = await fetch(`${API_BASE}/health`);
          const data = await response.json();

          if (response.ok) {
            updateStatus("✅ DGM Canvas está online!", "success");
            displayResults("🏥 Health Check", data);
          } else {
            updateStatus("❌ Erro na conexão", "error");
          }
        } catch (error) {
          updateStatus(
            `❌ Erro: ${error.message}. Certifique-se de que o DGM Canvas está rodando!`,
            "error"
          );
        }
      }

      async function sendTestData() {
        updateStatus("📤 Enviando dados de teste...", "info");

        const testData = {
          id: "test_" + Date.now(),
          timestamp: new Date().toISOString(),
          patrimonio: {
            total: 50000,
            alocado: 35000,
            restante: 15000,
            percentualAlocado: 70.0,
          },
          ativos: {
            escolhidos: [
              { product: "Teste A", category: "teste" },
              { product: "Teste B", category: "teste" },
            ],
            alocacao: [
              {
                key: "teste-a",
                category: "teste",
                product: "Teste A",
                value: 20000,
                percentage: 57.14,
                formatted: {
                  value: "R$ 20.000,00",
                  percentage: "57.14%",
                },
              },
              {
                key: "teste-b",
                category: "teste",
                product: "Teste B",
                value: 15000,
                percentage: 42.86,
                formatted: {
                  value: "R$ 15.000,00",
                  percentage: "42.86%",
                },
              },
            ],
          },
          usuario: {
            nome: "João Teste",
            email: "<EMAIL>",
            hasUserData: true,
          },
          metadata: {
            source: "test-page",
            version: "1.0.0",
            hasTypebot: false,
            sessionId: "test_session_" + Date.now(),
          },
        };

        try {
          const response = await fetch(`${API_BASE}/data`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-Source": "test-page",
            },
            body: JSON.stringify(testData),
          });

          const result = await response.json();

          if (response.ok) {
            updateStatus("✅ Dados enviados com sucesso!", "success");
            displayResults("📊 Resposta do Servidor", result);
          } else {
            updateStatus("❌ Erro ao enviar dados", "error");
            displayResults("❌ Erro", result);
          }
        } catch (error) {
          updateStatus(`❌ Erro: ${error.message}`, "error");
        }
      }

      async function sendRealisticData() {
        updateStatus("💰 Enviando dados realistas...", "info");

        const realisticData = {
          id: "realistic_" + Date.now(),
          timestamp: new Date().toISOString(),
          patrimonio: {
            total: 250000,
            alocado: 225000,
            restante: 25000,
            percentualAlocado: 90.0,
          },
          ativos: {
            escolhidos: [
              { product: "Tesouro Direto", category: "renda-fixa" },
              { product: "Ações Brasil", category: "renda-variavel" },
              { product: "FII", category: "fundos-imobiliarios" },
            ],
            alocacao: [
              {
                key: "renda-fixa-tesouro",
                category: "renda-fixa",
                product: "Tesouro Direto",
                value: 125000,
                percentage: 55.56,
                formatted: {
                  value: "R$ 125.000,00",
                  percentage: "55.56%",
                },
              },
              {
                key: "renda-variavel-acoes",
                category: "renda-variavel",
                product: "Ações Brasil",
                value: 75000,
                percentage: 33.33,
                formatted: {
                  value: "R$ 75.000,00",
                  percentage: "33.33%",
                },
              },
              {
                key: "fundos-fii",
                category: "fundos-imobiliarios",
                product: "FII",
                value: 25000,
                percentage: 11.11,
                formatted: {
                  value: "R$ 25.000,00",
                  percentage: "11.11%",
                },
              },
            ],
            resumo: {
              totalItems: 3,
              totalValue: 225000,
              categories: [
                {
                  name: "renda-fixa",
                  value: 125000,
                  count: 1,
                  percentage: 55.56,
                },
                {
                  name: "renda-variavel",
                  value: 75000,
                  count: 1,
                  percentage: 33.33,
                },
                {
                  name: "fundos-imobiliarios",
                  value: 25000,
                  count: 1,
                  percentage: 11.11,
                },
              ],
            },
          },
          usuario: {
            nome: "Maria Silva",
            email: "<EMAIL>",
            hasUserData: true,
          },
          metadata: {
            source: "app-calc-reino",
            version: "1.0.0",
            hasTypebot: true,
            typebotSessionId: "session_abc123",
            typebotResultId: "result_def456",
            userAgent: navigator.userAgent,
            sessionId: "realistic_session_" + Date.now(),
            submittedAt: new Date().toISOString(),
          },
        };

        try {
          const response = await fetch(`${API_BASE}/data`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-Source": "app-calc-reino",
              "X-Version": "1.0.0",
            },
            body: JSON.stringify(realisticData),
          });

          const result = await response.json();

          if (response.ok) {
            updateStatus("💰 Dados realistas enviados com sucesso!", "success");
            displayResults("🎯 Resposta do Servidor", result);
          } else {
            updateStatus("❌ Erro ao enviar dados realistas", "error");
            displayResults("❌ Erro", result);
          }
        } catch (error) {
          updateStatus(`❌ Erro: ${error.message}`, "error");
        }
      }

      async function checkLatestData() {
        updateStatus("🔍 Verificando últimos dados...", "info");

        try {
          const response = await fetch(`${API_BASE}/latest`);
          const result = await response.json();

          if (response.ok) {
            if (result.data) {
              updateStatus("📋 Últimos dados encontrados!", "success");
              displayResults("📊 Últimos Dados Recebidos", result);
            } else {
              updateStatus("📭 Nenhum dado encontrado ainda", "info");
            }
          } else {
            updateStatus("❌ Erro ao verificar dados", "error");
          }
        } catch (error) {
          updateStatus(`❌ Erro: ${error.message}`, "error");
        }
      }

      // Auto-test connection on load
      window.addEventListener("load", () => {
        setTimeout(testHealthCheck, 1000);
      });
    </script>
  </body>
</html>
