# 🚀 Guia Completo de Deploy - App Calc Reino & DGM Canvas

Este guia fornece instruções completas para fazer deploy de ambos os projetos (app-calc-reino e dgm-canvas) em produção, incluindo alternativas de hospedagem gratuita e paga.

## 📋 Índice

- [Visão Geral da Arquitetura](#visão-geral-da-arquitetura)
- [Opções de Hospedagem](#opções-de-hospedagem)
- [Deploy do App-Calc-Reino](#deploy-do-app-calc-reino)
- [Deploy do DGM Canvas](#deploy-do-dgm-canvas)
- [Configuração do Banco de Dados](#configuração-do-banco-de-dados)
- [Variáveis de Ambiente](#variáveis-de-ambiente)
- [Configuração de DNS e Domínios](#configuração-de-dns-e-domínios)
- [Monitoramento e Logs](#monitoramento-e-logs)
- [Backup e Recuperação](#backup-e-recuperação)
- [Troubleshooting](#troubleshooting)

## 🏗️ Visão Geral da Arquitetura

### App-Calc-Reino

- **Tipo**: Aplicação híbrida TypeScript + Webflow
- **Backend**: Node.js/Express com módulos TypeScript
- **Frontend**: Templates Webflow + JavaScript modular
- **Banco**: Supabase PostgreSQL
- **API**: Express.js middleware para integração com DGM Canvas

### DGM Canvas

- **Tipo**: PWA React + Vite
- **Frontend**: React 19.1.0 com DGM.js
- **Desenvolvimento**: Vite 7.0.4
- **API**: Middleware customizado para comunicação com app-calc-reino
- **Canvas**: DGM.js para visualização e manipulação gráfica

### Integração

- História API conectando ambos os projetos
- Supabase como banco unificado
- CORS configurado para comunicação entre aplicações

## 🌐 Opções de Hospedagem

### 🆓 **Alternativas Gratuitas Recomendadas**

#### 1. **Vercel** (Recomendado para Frontend)

- **Melhor para**: DGM Canvas (React/Vite)
- **Gratuito**: 100GB bandwidth, 100K funções serverless/mês
- **Limitações**: Sem backend persistente, timeout de 10s em funções
- **Setup**: Git deploy automático, preview de PRs

#### 2. **Netlify** (Alternativa Frontend)

- **Melhor para**: DGM Canvas com funções serverless simples
- **Gratuito**: 100GB bandwidth, 300 min build/mês
- **Recursos**: Forms, edge functions, split testing
- **Monetização**: Permitida no plano gratuito

#### 3. **Render** (Melhor para Backend)

- **Melhor para**: App-calc-reino (Node.js/Express)
- **Gratuito**: 512MB RAM, 0.1 CPU, web services
- **Recursos**: Background workers, cron jobs, PostgreSQL
- **Limitação**: Apps "dormem" após 15min inatividade

#### 4. **Railway** (Alternativa Fullstack)

- **Melhor para**: Ambos os projetos
- **Gratuito**: $5 em créditos mensais
- **Recursos**: GitHub integration, preview environments
- **Escalabilidade**: 8GB RAM / 8 vCPU por serviço

#### 5. **Fly.io** (Para aplicações globais)

- **Melhor para**: Deploy próximo aos usuários
- **Gratuito**: 3 máquinas compartilhadas, 3GB storage
- **Recursos**: Deploy global, containers Docker
- **Preço**: A partir de $4.58/mês para recursos adicionais

### 💰 **Alternativas Pagas Recomendadas**

#### 1. **DigitalOcean App Platform**

- **Preço**: A partir de $5/mês
- **Recursos**: VPS configurável, managed databases
- **Vantagem**: Controle total sobre infraestrutura

#### 2. **Heroku**

- **Preço**: A partir de $5/mês (Eco plan)
- **Recursos**: Add-ons extensos, escalabilidade simples
- **Desvantagem**: Preços podem escalar rapidamente

#### 3. **AWS/GCP/Azure**

- **Preço**: Variável (a partir de $3/mês)
- **Recursos**: Máxima flexibilidade e escalabilidade
- **Requerimento**: Conhecimento técnico avançado

## 🚀 Deploy do App-Calc-Reino

### Preparação

1. **Instalar dependências**:

```bash
cd app-calc-reino
pnpm install
```

2. **Build de produção**:

```bash
pnpm run build
```

3. **Testar localmente**:

```bash
pnpm run serve
```

### Deploy no Render (Recomendado)

1. **Criar conta no Render**: <https://render.com>
2. **Conectar repositório Git**
3. **Configurar serviço**:
   - **Build Command**: `pnpm install && pnpm run build`
   - **Start Command**: `node dist/server.js`
   - **Environment**: Node
   - **Plan**: Free (ou Starter $7/mês)

4. **Variáveis de ambiente**:

```env
NODE_ENV=production
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
TYPEBOT_API_URL=your_typebot_url
TYPEBOT_API_TOKEN=your_typebot_token
DGM_CANVAS_URL=https://your-dgm-canvas.vercel.app
CORS_ORIGIN=https://your-dgm-canvas.vercel.app
```

### Deploy no Railway (Alternativa)

1. **Criar conta no Railway**: <https://railway.app>
2. **Deploy from GitHub**:

```bash
# railway.json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "pnpm run serve",
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### Deploy no DigitalOcean App Platform

1. **Criar arquivo de especificação** (`app.yaml`):

```yaml
name: app-calc-reino
services:
- name: web
  source_dir: /
  github:
    repo: your-username/app-calc-reino
    branch: main
  run_command: pnpm run serve
  build_command: pnpm install && pnpm run build
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
  - key: SUPABASE_URL
    value: your_supabase_url
    type: SECRET
```

### Dockerfile para deployment customizado

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Instalar pnpm
RUN npm install -g pnpm

# Copiar arquivos de dependências
COPY package.json pnpm-lock.yaml ./

# Instalar dependências
RUN pnpm install --frozen-lockfile

# Copiar código fonte
COPY . .

# Build da aplicação
RUN pnpm run build

# Expor porta
EXPOSE 3000

# Comando de inicialização
CMD ["pnpm", "run", "serve"]
```

## 🎨 Deploy do DGM Canvas

### Preparação

1. **Instalar dependências**:

```bash
cd dgm-canvas
npm install
```

2. **Build de produção**:

```bash
npm run build
```

3. **Preview local**:

```bash
npm run preview
```

### Deploy no Vercel (Recomendado)

1. **Instalar Vercel CLI**:

```bash
npm i -g vercel
```

2. **Deploy**:

```bash
vercel --prod
```

3. **Configurar `vercel.json`**:

```json
{
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "env": {
    "VITE_API_BASE_URL": "https://your-app-calc-reino.onrender.com",
    "VITE_SUPABASE_URL": "your_supabase_url",
    "VITE_SUPABASE_ANON_KEY": "your_supabase_anon_key"
  },
  "functions": {
    "api/**/*.js": {
      "runtime": "nodejs18.x"
    }
  },
  "rewrites": [
    {
      "source": "/api/:path*",
      "destination": "https://your-app-calc-reino.onrender.com/api/:path*"
    }
  ]
}
```

### Deploy no Netlify (Alternativa)

1. **Criar `netlify.toml`**:

```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"

[[redirects]]
  from = "/api/*"
  to = "https://your-app-calc-reino.onrender.com/api/:splat"
  status = 200
  force = true

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
```

### Deploy no GitHub Pages (Gratuito)

1. **Configurar GitHub Actions** (`.github/workflows/deploy.yml`):

```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build
      run: npm run build
      env:
        VITE_API_BASE_URL: ${{ secrets.VITE_API_BASE_URL }}
        VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}

    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

## 🗄️ Configuração do Banco de Dados

### Setup Supabase

1. **Criar projeto no Supabase**: <https://supabase.com>
2. **Executar migrations**:

```sql
-- Executar scripts em ordem:
-- 1. database/supabase-setup.sql
-- 2. database/add-user-info-columns.sql
-- 3. database/salesforce-sync-migration.sql
-- 4. database/quick-fix-rls.sql
```

3. **Configurar RLS (Row Level Security)**:

```sql
-- Habilitar RLS nas tabelas principais
ALTER TABLE calculator_submissions ENABLE ROW LEVEL SECURITY;

-- Políticas básicas
CREATE POLICY "Enable read access for all users" ON calculator_submissions
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users" ON calculator_submissions
    FOR INSERT WITH CHECK (true);
```

4. **Obter credenciais**:
   - URL do projeto
   - Chave anônima (anon key)
   - Chave de serviço (service role key)

### Configuração de Backup

1. **Backup automático no Supabase** (incluído nos planos pagos)
2. **Script de backup manual**:

```bash
#!/bin/bash
# backup.sh
pg_dump "postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres" > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 🔧 Variáveis de Ambiente

### App-Calc-Reino (.env.production)

```env
NODE_ENV=production
PORT=3000

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Typebot
TYPEBOT_API_URL=https://your-typebot-instance.com
TYPEBOT_API_TOKEN=your_typebot_token

# CORS
CORS_ORIGIN=https://your-dgm-canvas-domain.com
ALLOWED_ORIGINS=https://your-dgm-canvas-domain.com,https://your-webflow-domain.webflow.io

# DGM Canvas Integration
DGM_CANVAS_URL=https://your-dgm-canvas-domain.com
HISTORY_API_ENABLED=true

# Security
SESSION_SECRET=your_session_secret_key
JWT_SECRET=your_jwt_secret_key

# Salesforce (se usado)
SALESFORCE_CONSUMER_KEY=your_salesforce_consumer_key
SALESFORCE_CONSUMER_SECRET=your_salesforce_consumer_secret
SALESFORCE_USERNAME=your_salesforce_username
SALESFORCE_PASSWORD=your_salesforce_password
SALESFORCE_SECURITY_TOKEN=your_salesforce_security_token
```

### DGM Canvas (.env.production)

```env
VITE_NODE_ENV=production

# API Configuration
VITE_API_BASE_URL=https://your-app-calc-reino-domain.com
VITE_HISTORY_API_ENDPOINT=https://your-app-calc-reino-domain.com/api

# Supabase
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key

# DGM Configuration
VITE_DGM_DEBUG=false
VITE_DGM_AUTO_SAVE=true

# Features
VITE_ENABLE_HISTORY_PANEL=true
VITE_ENABLE_DATA_MANAGER=true
VITE_ENABLE_MANUAL_TESTS=false
```

## 🌍 Configuração de DNS e Domínios

### Configuração de Subdomínios

1. **App-Calc-Reino**: `api.seudominio.com`
2. **DGM Canvas**: `canvas.seudominio.com` ou `app.seudominio.com`

### Configuração DNS (exemplo)

```dns
# Registros A/CNAME para cada serviço
api      CNAME   app-calc-reino.onrender.com
canvas   CNAME   dgm-canvas.vercel.app
www      CNAME   your-webflow-site.webflow.io
```

### SSL/HTTPS

- **Vercel/Netlify**: SSL automático
- **Render**: SSL automático com custom domains
- **DigitalOcean**: Let's Encrypt automático

## 📊 Monitoramento e Logs

### Ferramentas Recomendadas (Gratuitas)

1. **Uptime Monitoring**:
   - UptimeRobot (gratuito até 50 monitores)
   - StatusCake (gratuito básico)

2. **Error Tracking**:
   - Sentry (gratuito até 5K erros/mês)

   ```javascript
   // Sentry setup
   import * as Sentry from "@sentry/node";
   
   Sentry.init({
     dsn: "your_sentry_dsn",
     environment: process.env.NODE_ENV,
   });
   ```

3. **Analytics**:
   - Google Analytics 4
   - Vercel Analytics (para apps no Vercel)

### Log Aggregation

```javascript
// logger.js - Configuração centralizada
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'app.log' })
  ]
});

export default logger;
```

## 💾 Backup e Recuperação

### Estratégia de Backup

1. **Banco de Dados**: Backup automático do Supabase
2. **Código**: Git repositories como backup
3. **Assets**: CDN/storage de terceiros

### Plano de Recuperação

1. **RTO (Recovery Time Objective)**: 1 hora
2. **RPO (Recovery Point Objective)**: 24 horas
3. **Procedimentos**:
   - Restore do banco via Supabase dashboard
   - Redeploy via Git
   - Verificação de integridade

## 🔧 Troubleshooting

### Problemas Comuns

#### 1. CORS Errors

```javascript
// Verificar configuração CORS
const corsOptions = {
  origin: process.env.CORS_ORIGIN.split(','),
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};
```

#### 2. Build Failures

```bash
# Limpar cache e reinstalar
rm -rf node_modules package-lock.json
npm install

# Verificar versões Node.js
node --version  # Deve ser 18+
```

#### 3. Database Connection Issues

```javascript
// Verificar connection string
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase credentials');
}
```

#### 4. Memory/Performance Issues

```javascript
// Configurar limits no PM2 ou container
{
  "apps": [{
    "name": "app-calc-reino",
    "script": "dist/server.js",
    "instances": 1,
    "max_memory_restart": "200M",
    "node_args": "--max-old-space-size=200"
  }]
}
```

### Checklist de Deploy

- [ ] Variáveis de ambiente configuradas
- [ ] Banco de dados migrado
- [ ] CORS configurado corretamente
- [ ] SSL/HTTPS ativo
- [ ] DNS apontando corretamente
- [ ] Monitoramento configurado
- [ ] Backup strategy implementada
- [ ] Logs centralizados
- [ ] Performance testada
- [ ] Security headers configurados

## 📞 Suporte e Recursos

### Documentação Oficial

- [Vercel Docs](https://vercel.com/docs)
- [Netlify Docs](https://docs.netlify.com/)
- [Render Docs](https://render.com/docs)
- [Supabase Docs](https://supabase.com/docs)

### Comunidades

- [Vercel Discord](https://vercel.com/discord)
- [Supabase Discord](https://discord.supabase.com/)
- [React Community](https://reactjs.org/community/support.html)

---

**Última atualização**: Janeiro 2025
**Versão do guia**: 1.0

> 💡 **Dica**: Comece sempre com os planos gratuitos para validar o setup, depois migre para planos pagos conforme necessário. Monitore sempre os limites de uso para evitar surpresas.
