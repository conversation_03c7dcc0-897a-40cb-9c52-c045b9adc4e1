# Salesforce Integration Configuration
# Add these environment variables to your deployment or local environment

# Salesforce Connected App Configuration
# Get these from your Salesforce org's Connected App setup
SALESFORCE_CLIENT_ID=your_salesforce_consumer_key_here
SALESFORCE_CLIENT_SECRET=your_salesforce_consumer_secret_here
SALESFORCE_USERNAME=your_salesforce_username_here
SALESFORCE_PASSWORD=your_salesforce_password_here
SALESFORCE_SECURITY_TOKEN=your_salesforce_security_token_here

# Salesforce Instance Configuration
# Use your Salesforce domain (e.g., https://yourcompany.my.salesforce.com)
SALESFORCE_INSTANCE_URL=https://login.salesforce.com

# API Configuration
SALESFORCE_API_VERSION=v59.0

# Custom Object API Names (update these to match your Salesforce setup)
SALESFORCE_CALCULATOR_OBJECT=Calculator_Submission__c

# Sync Configuration
SALESFORCE_SYNC_ENABLED=true
SALESFORCE_RETRY_ATTEMPTS=3
SALESFORCE_RETRY_DELAY=5000

# Debug Configuration (set to false in production)
SALESFORCE_DEBUG_MODE=true
