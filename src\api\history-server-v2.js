/**
 * History API Server for DGM Canvas Integration
 * Serves historical data endpoints from Supabase
 */

/* global URL, fetch */

import {
  getRecentSubmissions,
  getSubmissionById,
  getSubmissionsByDateRange,
  getSubmissionStats,
  searchSubmissions,
} from './history-endpoints.js';

/**
 * Setup history API endpoints
 * @param {Object} server - Server instance (Express or Vite dev server)
 */
export function setupHistoryAPI(server) {
  // eslint-disable-next-line no-console
  console.log('🚀 [HistoryAPI] Setting up history endpoints for DGM Canvas...');

  // CORS headers for all history endpoints
  const setCORSHeaders = (res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Source, X-Version');
  };

  // Helper to handle API responses
  const sendResponse = (res, result) => {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = result.success ? 200 : 500;
    res.end(JSON.stringify(result));
  };

  // Helper to handle errors
  const sendError = (res, error, endpoint) => {
    setCORSHeaders(res);
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 500;
    res.end(
      JSON.stringify({
        success: false,
        error: error.message,
        endpoint,
      })
    );
  };

  // GET /api/history/recent - Get recent submissions
  server.middlewares.use('/api/history/recent', async (req, res, next) => {
    if (req.method === 'OPTIONS') {
      setCORSHeaders(res);
      res.statusCode = 200;
      res.end();
      return;
    }

    if (req.method === 'GET') {
      try {
        setCORSHeaders(res);

        const url = new URL(req.url, `http://${req.headers.host}`);
        const limit = parseInt(url.searchParams.get('limit')) || 10;
        const orderBy = url.searchParams.get('orderBy') || 'created_at';

        const result = await getRecentSubmissions(limit, orderBy);
        sendResponse(res, result);
      } catch (error) {
        console.error('❌ [HistoryAPI] Error in /api/history/recent:', error);
        sendError(res, error, '/api/history/recent');
      }
    } else {
      next();
    }
  });

  // GET /api/history/stats - Get submission statistics
  server.middlewares.use('/api/history/stats', async (req, res, next) => {
    if (req.method === 'OPTIONS') {
      setCORSHeaders(res);
      res.statusCode = 200;
      res.end();
      return;
    }

    if (req.method === 'GET') {
      try {
        setCORSHeaders(res);
        const result = await getSubmissionStats();
        sendResponse(res, result);
      } catch (error) {
        console.error('❌ [HistoryAPI] Error in /api/history/stats:', error);
        sendError(res, error, '/api/history/stats');
      }
    } else {
      next();
    }
  });

  // GET /api/history/search - Search submissions
  server.middlewares.use('/api/history/search', async (req, res, next) => {
    if (req.method === 'OPTIONS') {
      setCORSHeaders(res);
      res.statusCode = 200;
      res.end();
      return;
    }

    if (req.method === 'GET') {
      try {
        setCORSHeaders(res);

        const url = new URL(req.url, `http://${req.headers.host}`);
        const searchTerm = url.searchParams.get('q');
        const limit = parseInt(url.searchParams.get('limit')) || 20;

        if (!searchTerm) {
          res.setHeader('Content-Type', 'application/json');
          res.statusCode = 400;
          res.end(
            JSON.stringify({
              success: false,
              error: 'q (search term) query parameter is required',
              example: '/api/history/search?q=joao&limit=10',
            })
          );
          return;
        }

        const result = await searchSubmissions(searchTerm, limit);
        sendResponse(res, result);
      } catch (error) {
        console.error('❌ [HistoryAPI] Error in /api/history/search:', error);
        sendError(res, error, '/api/history/search');
      }
    } else {
      next();
    }
  });

  // GET /api/history/date-range - Get submissions by date range
  server.middlewares.use('/api/history/date-range', async (req, res, next) => {
    if (req.method === 'OPTIONS') {
      setCORSHeaders(res);
      res.statusCode = 200;
      res.end();
      return;
    }

    if (req.method === 'GET') {
      try {
        setCORSHeaders(res);

        const url = new URL(req.url, `http://${req.headers.host}`);
        const startDate = url.searchParams.get('startDate');
        const endDate = url.searchParams.get('endDate');
        const limit = parseInt(url.searchParams.get('limit')) || 50;

        if (!startDate || !endDate) {
          res.setHeader('Content-Type', 'application/json');
          res.statusCode = 400;
          res.end(
            JSON.stringify({
              success: false,
              error: 'startDate and endDate query parameters are required',
              example: '/api/history/date-range?startDate=2025-01-01&endDate=2025-01-31',
            })
          );
          return;
        }

        const result = await getSubmissionsByDateRange(startDate, endDate, limit);
        sendResponse(res, result);
      } catch (error) {
        console.error('❌ [HistoryAPI] Error in /api/history/date-range:', error);
        sendError(res, error, '/api/history/date-range');
      }
    } else {
      next();
    }
  });

  // GET /api/history/:id - Get single submission by ID
  server.middlewares.use('/api/history/', async (req, res, next) => {
    if (req.method === 'OPTIONS') {
      setCORSHeaders(res);
      res.statusCode = 200;
      res.end();
      return;
    }

    if (req.method === 'GET') {
      try {
        setCORSHeaders(res);

        const urlPath = req.url.replace('/api/history/', '');
        const submissionId = urlPath.split('?')[0];

        if (!submissionId || submissionId.length < 10) {
          res.setHeader('Content-Type', 'application/json');
          res.statusCode = 400;
          res.end(
            JSON.stringify({
              success: false,
              error: 'Valid submission ID is required',
              example: '/api/history/12345678-1234-1234-1234-123456789012',
            })
          );
          return;
        }

        const result = await getSubmissionById(submissionId);
        res.statusCode = result.success ? (result.data ? 200 : 404) : 500;
        sendResponse(res, result);
      } catch (error) {
        console.error('❌ [HistoryAPI] Error in /api/history/:id:', error);
        sendError(res, error, '/api/history/:id');
      }
    } else {
      next();
    }
  });

  // GET /api/history - List available endpoints
  server.middlewares.use('/api/history', (req, res, next) => {
    if (req.method === 'OPTIONS') {
      setCORSHeaders(res);
      res.statusCode = 200;
      res.end();
      return;
    }

    if (req.method === 'GET' && req.url === '/api/history') {
      setCORSHeaders(res);
      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 200;
      res.end(
        JSON.stringify({
          success: true,
          message: 'DGM Canvas History API',
          version: '1.0.0',
          endpoints: {
            recent: {
              path: '/api/history/recent',
              method: 'GET',
              description: 'Get recent submissions',
              parameters: {
                limit: 'Number of records (default: 10)',
                orderBy: 'Field to order by (default: created_at)',
              },
              example: '/api/history/recent?limit=5&orderBy=created_at',
            },
            stats: {
              path: '/api/history/stats',
              method: 'GET',
              description: 'Get submission statistics',
              example: '/api/history/stats',
            },
            search: {
              path: '/api/history/search',
              method: 'GET',
              description: 'Search submissions by user info',
              parameters: {
                q: 'Search term (required)',
                limit: 'Maximum records (default: 20)',
              },
              example: '/api/history/search?q=joao&limit=10',
            },
            dateRange: {
              path: '/api/history/date-range',
              method: 'GET',
              description: 'Get submissions by date range',
              parameters: {
                startDate: 'Start date (ISO format, required)',
                endDate: 'End date (ISO format, required)',
                limit: 'Maximum records (default: 50)',
              },
              example: '/api/history/date-range?startDate=2025-01-01&endDate=2025-01-31',
            },
            single: {
              path: '/api/history/:id',
              method: 'GET',
              description: 'Get single submission by ID',
              example: '/api/history/12345678-1234-1234-1234-123456789012',
            },
          },
          timestamp: new Date().toISOString(),
        })
      );
    } else {
      next();
    }
  });

  // eslint-disable-next-line no-console
  console.log('✅ [HistoryAPI] All history endpoints configured successfully');
}

export default setupHistoryAPI;
