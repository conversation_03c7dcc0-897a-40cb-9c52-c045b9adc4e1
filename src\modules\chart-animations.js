/**
 * Chart Animation System
 * Handles GSAP animations for charts and data visualizations
 */
export class ChartAnimationSystem {
  constructor() {
    this.isInitialized = false;
    this.gsap = null;
    this.charts = new Map();
    this.animations = new Map();
    this.isAnimating = false;
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    document.addEventListener('DOMContentLoaded', () => {
      this.waitForGSAP();
    });

    this.isInitialized = true;
  }

  waitForGSAP() {
    if (window.gsap) {
      this.gsap = window.gsap;
      this.initializeCharts();
    } else {
      setTimeout(() => this.waitForGSAP(), 50);
    }
  }

  initializeCharts() {
    this.findChartElements();
    this.setupChartObservers();
    this.initializeDefaultAnimations();
  }

  findChartElements() {
    // Elementos de gráfico de pizza
    const pieCharts = document.querySelectorAll('.chart-pie, [data-chart="pie"]');
    pieCharts.forEach((chart, index) => {
      this.charts.set(`pie-${index}`, {
        element: chart,
        type: 'pie',
        segments: chart.querySelectorAll('.chart-segment, .pie-segment'),
        labels: chart.querySelectorAll('.chart-label, .pie-label'),
        center: chart.querySelector('.chart-center, .pie-center'),
      });
    });

    // Elementos de gráfico de barras
    const barCharts = document.querySelectorAll('.chart-bar, [data-chart="bar"]');
    barCharts.forEach((chart, index) => {
      this.charts.set(`bar-${index}`, {
        element: chart,
        type: 'bar',
        bars: chart.querySelectorAll('.chart-bar-item, .bar-item'),
        labels: chart.querySelectorAll('.chart-label, .bar-label'),
        values: chart.querySelectorAll('.chart-value, .bar-value'),
      });
    });

    // Elementos de gráfico de linha
    const lineCharts = document.querySelectorAll('.chart-line, [data-chart="line"]');
    lineCharts.forEach((chart, index) => {
      this.charts.set(`line-${index}`, {
        element: chart,
        type: 'line',
        lines: chart.querySelectorAll('.chart-line-path, .line-path'),
        points: chart.querySelectorAll('.chart-point, .line-point'),
        labels: chart.querySelectorAll('.chart-label, .line-label'),
      });
    });
  }

  setupChartObservers() {
    // Observer para detectar quando gráficos entram na viewport
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const chartKey = this.findChartKey(entry.target);
            if (chartKey && !this.isAnimating) {
              this.animateChart(chartKey);
            }
          }
        });
      },
      {
        threshold: 0.3,
        rootMargin: '0px 0px -50px 0px',
      }
    );

    this.charts.forEach((chart) => {
      observer.observe(chart.element);
    });
  }

  findChartKey(element) {
    for (const [key, chart] of this.charts.entries()) {
      if (chart.element === element) {
        return key;
      }
    }
    return null;
  }

  initializeDefaultAnimations() {
    // Define animações padrão para diferentes tipos de gráfico
    this.charts.forEach((chart, key) => {
      this.prepareChartForAnimation(key);
    });
  }

  prepareChartForAnimation(chartKey) {
    const chart = this.charts.get(chartKey);
    if (!chart) return;

    switch (chart.type) {
      case 'pie':
        this.preparePieChart(chart);
        break;
      case 'bar':
        this.prepareBarChart(chart);
        break;
      case 'line':
        this.prepareLineChart(chart);
        break;
    }
  }

  preparePieChart(chart) {
    // Estado inicial dos segmentos de pizza
    chart.segments.forEach((segment) => {
      this.gsap.set(segment, {
        scale: 0,
        rotation: -90,
        transformOrigin: 'center',
        opacity: 0,
      });
    });

    // Estado inicial dos labels
    chart.labels.forEach((label) => {
      this.gsap.set(label, {
        opacity: 0,
        y: 20,
      });
    });

    // Estado inicial do centro
    if (chart.center) {
      this.gsap.set(chart.center, {
        scale: 0,
        opacity: 0,
      });
    }
  }

  prepareBarChart(chart) {
    // Estado inicial das barras
    chart.bars.forEach((bar) => {
      this.gsap.set(bar, {
        scaleY: 0,
        transformOrigin: 'bottom',
        opacity: 0,
      });
    });

    // Estado inicial dos labels e valores
    [...chart.labels, ...chart.values].forEach((element) => {
      this.gsap.set(element, {
        opacity: 0,
        y: 10,
      });
    });
  }

  prepareLineChart(chart) {
    // Estado inicial das linhas
    chart.lines.forEach((line) => {
      const pathLength = line.getTotalLength ? line.getTotalLength() : 0;
      this.gsap.set(line, {
        strokeDasharray: pathLength,
        strokeDashoffset: pathLength,
        opacity: 0,
      });
    });

    // Estado inicial dos pontos
    chart.points.forEach((point) => {
      this.gsap.set(point, {
        scale: 0,
        opacity: 0,
      });
    });

    // Estado inicial dos labels
    chart.labels.forEach((label) => {
      this.gsap.set(label, {
        opacity: 0,
        y: 10,
      });
    });
  }

  async animateChart(chartKey) {
    const chart = this.charts.get(chartKey);
    if (!chart || this.isAnimating) return;

    this.isAnimating = true;

    try {
      switch (chart.type) {
        case 'pie':
          await this.animatePieChart(chart);
          break;
        case 'bar':
          await this.animateBarChart(chart);
          break;
        case 'line':
          await this.animateLineChart(chart);
          break;
      }
    } finally {
      this.isAnimating = false;
    }

    // Salva a animação para possível replay
    this.animations.set(chartKey, Date.now());
  }

  async animatePieChart(chart) {
    const timeline = this.gsap.timeline();

    // Anima centro primeiro
    if (chart.center) {
      timeline.to(chart.center, {
        scale: 1,
        opacity: 1,
        duration: 0.5,
        ease: 'back.out(1.7)',
      });
    }

    // Anima segmentos com stagger
    timeline.to(
      chart.segments,
      {
        scale: 1,
        rotation: 0,
        opacity: 1,
        duration: 0.8,
        ease: 'back.out(1.2)',
        stagger: 0.1,
      },
      '-=0.2'
    );

    // Anima labels
    timeline.to(
      chart.labels,
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: 'power2.out',
        stagger: 0.05,
      },
      '-=0.4'
    );

    return timeline;
  }

  async animateBarChart(chart) {
    const timeline = this.gsap.timeline();

    // Anima barras com stagger
    timeline.to(chart.bars, {
      scaleY: 1,
      opacity: 1,
      duration: 0.8,
      ease: 'power2.out',
      stagger: 0.1,
    });

    // Anima labels e valores
    timeline.to(
      [...chart.labels, ...chart.values],
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: 'power2.out',
        stagger: 0.03,
      },
      '-=0.4'
    );

    return timeline;
  }

  async animateLineChart(chart) {
    const timeline = this.gsap.timeline();

    // Anima linhas (desenho progressivo)
    timeline.to(chart.lines, {
      strokeDashoffset: 0,
      opacity: 1,
      duration: 1.2,
      ease: 'power2.inOut',
      stagger: 0.2,
    });

    // Anima pontos
    timeline.to(
      chart.points,
      {
        scale: 1,
        opacity: 1,
        duration: 0.5,
        ease: 'back.out(1.7)',
        stagger: 0.05,
      },
      '-=0.6'
    );

    // Anima labels
    timeline.to(
      chart.labels,
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: 'power2.out',
        stagger: 0.03,
      },
      '-=0.8'
    );

    return timeline;
  }

  // Métodos para animações de dados dinâmicos

  updateChartData(chartKey, newData) {
    const chart = this.charts.get(chartKey);
    if (!chart) return;

    switch (chart.type) {
      case 'pie':
        this.updatePieChartData(chart, newData);
        break;
      case 'bar':
        this.updateBarChartData(chart, newData);
        break;
      case 'line':
        this.updateLineChartData(chart, newData);
        break;
    }
  }

  updatePieChartData(chart, data) {
    // Anima mudanças nos dados do gráfico de pizza
    const timeline = this.gsap.timeline();

    chart.segments.forEach((segment, index) => {
      if (data[index]) {
        const newRotation = data[index].angle || 0;
        const newScale = data[index].scale || 1;

        timeline.to(
          segment,
          {
            rotation: newRotation,
            scale: newScale,
            duration: 0.8,
            ease: 'power2.inOut',
          },
          index * 0.1
        );
      }
    });
  }

  updateBarChartData(chart, data) {
    // Anima mudanças nos dados do gráfico de barras
    const timeline = this.gsap.timeline();

    chart.bars.forEach((bar, index) => {
      if (data[index]) {
        const newHeight = data[index].height || 1;

        timeline.to(
          bar,
          {
            scaleY: newHeight,
            duration: 0.6,
            ease: 'power2.out',
          },
          index * 0.05
        );
      }
    });
  }

  updateLineChartData(chart, data) {
    // Anima mudanças nos dados do gráfico de linha
    chart.points.forEach((point, index) => {
      if (data[index]) {
        this.gsap.to(point, {
          x: data[index].x,
          y: data[index].y,
          duration: 0.8,
          ease: 'power2.inOut',
        });
      }
    });
  }

  // Métodos de controle público

  replayChart(chartKey) {
    this.prepareChartForAnimation(chartKey);
    setTimeout(() => {
      this.animateChart(chartKey);
    }, 100);
  }

  pauseAllAnimations() {
    this.gsap.globalTimeline.pause();
  }

  resumeAllAnimations() {
    this.gsap.globalTimeline.resume();
  }

  addCustomChart(key, element, type, config) {
    const chart = {
      element,
      type,
      ...config,
    };

    this.charts.set(key, chart);
    this.prepareChartForAnimation(key);

    // Adiciona observer se necessário
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !this.isAnimating) {
            this.animateChart(key);
          }
        });
      },
      { threshold: 0.3 }
    );

    observer.observe(element);
  }

  removeChart(chartKey) {
    this.charts.delete(chartKey);
    this.animations.delete(chartKey);
  }

  getChartElement(chartKey) {
    const chart = this.charts.get(chartKey);
    return chart ? chart.element : null;
  }

  isChartAnimated(chartKey) {
    return this.animations.has(chartKey);
  }
}
