import { expect, Page, test } from '@playwright/test';

/**
 * Test para detectar memory leaks e loops infinitos no input principal
 * do sistema PatrimonySyncSystem
 */
test.describe('PatrimonySyncSystem Memory Leak Detection', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();

    // Monitor console errors
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.log('Console Error:', msg.text());
      }
    });

    // Monitor page errors
    page.on('pageerror', (error) => {
      console.log('Page Error:', error.message);
    });
  });

  test('should detect memory leaks when interacting with main input', async () => {
    // Navegar para a página local
    await page.goto('http://localhost:3000');

    // Aguardar a página carregar completamente
    await page.waitForLoadState('networkidle');

    // Aguardar o sistema estar inicializado
    await page.waitForFunction(
      () => {
        return window.ReinoCalculator && window.ReinoCalculator.app;
      },
      { timeout: 10000 }
    );

    // Encontrar o input principal
    const mainInput = page.locator('[is-main="true"]');
    await expect(mainInput).toBeVisible();

    // Monitorar performance antes da interação
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory
        ? {
            usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
            totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          }
        : null;
    });

    console.log('Initial Memory:', initialMemory);

    // Contar event listeners antes da interação
    const initialListenerCount = await page.evaluate(() => {
      const mainInput = document.querySelector('[is-main="true"]');
      if (!mainInput) return 0;

      // Hack para contar listeners (não é API oficial)
      const listeners = (mainInput as any)._events || {};
      return Object.keys(listeners).length;
    });

    console.log('Initial Event Listeners:', initialListenerCount);

    // Simular digitação no input principal
    await mainInput.click();
    await mainInput.fill('');

    // Digitar valores gradualmente para detectar loops
    const testValues = ['1', '10', '100', '1000', '10000'];

    for (const value of testValues) {
      console.log(`Testing with value: ${value}`);

      // Limpar e digitar novo valor
      await mainInput.fill('');
      await mainInput.type(value, { delay: 100 });

      // Aguardar um pouco para processar
      await page.waitForTimeout(500);

      // Verificar se a página ainda responde
      const isResponsive = await page.evaluate(() => {
        return document.readyState === 'complete';
      });

      if (!isResponsive) {
        console.error(`Page became unresponsive at value: ${value}`);
        break;
      }

      // Verificar memória atual
      const currentMemory = await page.evaluate(() => {
        return (performance as any).memory
          ? {
              usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
              totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
            }
          : null;
      });

      if (currentMemory && initialMemory) {
        const memoryIncrease = currentMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
        console.log(`Memory increase: ${memoryIncrease} bytes`);

        // Se o aumento de memória for muito grande, pode indicar leak
        if (memoryIncrease > 50 * 1024 * 1024) {
          // 50MB
          console.error(`Potential memory leak detected! Memory increase: ${memoryIncrease} bytes`);
        }
      }
    }

    // Verificar se há loops infinitos detectando mudanças rápidas no DOM
    const rapidDOMChanges = await page.evaluate(() => {
      let changeCount = 0;
      const observer = new MutationObserver(() => {
        changeCount++;
      });

      observer.observe(document.body, {
        subtree: true,
        attributes: true,
        childList: true,
        characterData: true,
      });

      // Aguardar 2 segundos e contar mudanças
      return new Promise((resolve) => {
        setTimeout(() => {
          observer.disconnect();
          resolve(changeCount);
        }, 2000);
      });
    });

    console.log(`DOM changes in 2 seconds: ${rapidDOMChanges}`);

    // Se há muitas mudanças no DOM, pode indicar loop infinito
    if (rapidDOMChanges > 1000) {
      console.error(`Potential infinite loop detected! DOM changes: ${rapidDOMChanges}`);
    }
  });

  test('should detect event listener multiplication', async () => {
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');

    // Aguardar inicialização
    await page.waitForFunction(
      () => {
        return window.ReinoCalculator && window.ReinoCalculator.app;
      },
      { timeout: 10000 }
    );

    // Simular múltiplas inicializações (que pode estar causando o problema)
    const listenerCounts = await page.evaluate(() => {
      const counts = [];
      const mainInput = document.querySelector('[is-main="true"]');

      if (!mainInput) return [];

      // Função para contar listeners de um elemento
      const countListeners = (element: Element) => {
        const events = ['input', 'change', 'currencyChange', 'focus', 'blur'];
        let total = 0;

        events.forEach((eventType) => {
          // Simulação de contagem - na realidade isso é difícil de fazer precisamente
          const listeners = (element as any)._listeners || {};
          if (listeners[eventType]) {
            total += listeners[eventType].length || 1;
          }
        });

        return total;
      };

      // Contar listeners inicial
      counts.push(countListeners(mainInput));

      // Simular reinicialização forçada
      if (window.ReinoCalculator && window.ReinoCalculator.systems) {
        const { patrimonySync } = window.ReinoCalculator.systems;
        if (patrimonySync && typeof patrimonySync.init === 'function') {
          // Tentar inicializar novamente (pode duplicar listeners)
          patrimonySync.init();
          counts.push(countListeners(mainInput));

          // Mais uma vez
          patrimonySync.init();
          counts.push(countListeners(mainInput));
        }
      }

      return counts;
    });

    console.log('Event listener counts:', listenerCounts);

    // Se os listeners estão aumentando, há duplicação
    if (listenerCounts.length > 1) {
      for (let i = 1; i < listenerCounts.length; i++) {
        if (listenerCounts[i] > listenerCounts[i - 1]) {
          console.error(
            `Event listeners are multiplying! Before: ${listenerCounts[i - 1]}, After: ${listenerCounts[i]}`
          );
        }
      }
    }
  });

  test('should detect circular dependency in handleMainValueChange', async () => {
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');

    await page.waitForFunction(
      () => {
        return window.ReinoCalculator && window.ReinoCalculator.app;
      },
      { timeout: 10000 }
    );

    // Monitorar chamadas de função para detectar loops
    const functionCallCounts = await page.evaluate(() => {
      const patrimonySync = window.ReinoCalculator?.systems?.patrimonySync;
      if (!patrimonySync) return {};

      const originalMethods = {};
      const callCounts = {};

      // Interceptar métodos principais
      const methodsToMonitor = [
        'handleMainValueChange',
        'updateAllAllocations',
        'validateAllAllocations',
        'handleInputChange',
        'updateSlider',
        'updatePercentageDisplay',
      ];

      methodsToMonitor.forEach((methodName) => {
        if (typeof patrimonySync[methodName] === 'function') {
          originalMethods[methodName] = patrimonySync[methodName];
          callCounts[methodName] = 0;

          patrimonySync[methodName] = function (...args) {
            callCounts[methodName]++;

            // Se uma função for chamada muitas vezes rapidamente, pode ser loop
            if (callCounts[methodName] > 100) {
              console.error(
                `Potential infinite loop in ${methodName}! Called ${callCounts[methodName]} times`
              );
              return; // Quebrar o loop
            }

            return originalMethods[methodName].apply(this, args);
          };
        }
      });

      // Simular mudança no input principal
      const mainInput = document.querySelector('[is-main="true"]');
      if (mainInput) {
        const event = new Event('input', { bubbles: true });
        Object.defineProperty(event, 'target', { value: mainInput });
        (mainInput as HTMLInputElement).value = '1000';
        mainInput.dispatchEvent(event);
      }

      // Aguardar um pouco e retornar contadores
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(callCounts);
        }, 2000);
      });
    });

    console.log('Function call counts:', functionCallCounts);

    // Verificar se alguma função foi chamada excessivamente
    Object.entries(functionCallCounts).forEach(([methodName, count]) => {
      if (count > 50) {
        console.error(`Method ${methodName} called ${count} times - potential infinite loop!`);
      }
    });
  });
});
