import { useState, useEffect } from 'react';

/**
 * DataControls Component
 * Provides interactive controls to adjust chart data values
 * with the ability to reset to original values
 */
export function DataControls({ currentData, onDataChange, isVisible = true }) {
  const [editableData, setEditableData] = useState(null);
  const [originalData, setOriginalData] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('values');
  const [isDragging, setIsDragging] = useState(null);

  // Initialize data when currentData changes
  useEffect(() => {
    if (currentData && currentData.ativos && currentData.ativos.alocacao) {
      // Store original data for reset functionality
      if (!originalData) {
        setOriginalData(JSON.parse(JSON.stringify(currentData)));
      }
      
      // Set editable data
      setEditableData(JSON.parse(JSON.stringify(currentData)));
    }
  }, [currentData, originalData]);

  // Handle value changes
  const handleValueChange = (index, newValue) => {
    if (!editableData) return;

    const newData = { ...editableData };
    const alocacao = [...newData.ativos.alocacao];
    
    // Update the value
    alocacao[index] = {
      ...alocacao[index],
      value: parseFloat(newValue) || 0
    };

    // Recalculate total and percentages
    const total = alocacao.reduce((sum, item) => sum + item.value, 0);
    
    // Update patrimonio total
    newData.patrimonio.total = total;
    newData.patrimonio.totalFormatted = formatCurrency(total);

    // Update percentages and formatted values
    alocacao.forEach(item => {
      const percentage = total > 0 ? (item.value / total) * 100 : 0;
      item.percentageFormatted = `${percentage.toFixed(1)}%`;
      item.valueFormatted = formatCurrency(item.value);
    });

    // Calculate remaining (not allocated)
    const allocated = total;
    const remaining = Math.max(0, total - allocated);
    newData.patrimonio.restante = remaining;
    newData.patrimonio.restanteFormatted = formatCurrency(remaining);
    newData.patrimonio.alocado = allocated;
    newData.patrimonio.alocadoFormatted = formatCurrency(allocated);

    newData.ativos.alocacao = alocacao;
    setEditableData(newData);
  };

  // Handle percentage changes
  const handlePercentageChange = (index, newPercentage) => {
    if (!editableData) return;

    const total = editableData.patrimonio.total;
    const newValue = (parseFloat(newPercentage) / 100) * total;
    handleValueChange(index, newValue);
  };

  // Apply changes to chart
  const applyChanges = () => {
    if (editableData && onDataChange) {
      onDataChange(editableData);
    }
  };

  // Reset to original values
  const resetToOriginal = () => {
    if (originalData) {
      const resetData = JSON.parse(JSON.stringify(originalData));
      setEditableData(resetData);
      if (onDataChange) {
        onDataChange(resetData);
      }
    }
  };

  // Auto-distribute remaining value
  const autoDistribute = () => {
    if (!editableData) return;

    const alocacao = [...editableData.ativos.alocacao];
    const total = editableData.patrimonio.total;
    const numItems = alocacao.length;
    const valuePerItem = total / numItems;

    alocacao.forEach((item, index) => {
      handleValueChange(index, valuePerItem);
    });
  };

  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Drag handlers for intuitive value adjustment
  const handleMouseDown = (index, e) => {
    setIsDragging({ index, startY: e.clientY, startValue: editableData.ativos.alocacao[index].value });
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    const deltaY = isDragging.startY - e.clientY; // Inverted for intuitive drag
    const sensitivity = 100; // Adjust sensitivity
    const newValue = Math.max(0, isDragging.startValue + (deltaY * sensitivity));
    
    handleValueChange(isDragging.index, newValue);
  };

  const handleMouseUp = () => {
    setIsDragging(null);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  if (!isVisible || !editableData) return null;

  const { ativos, patrimonio, usuario } = editableData;
  const total = patrimonio.total;

  return (
    <div style={styles.container}>
      {/* Toggle Button */}
      <button 
        onClick={() => setIsExpanded(!isExpanded)}
        style={styles.toggleButton}
        title="Editar Valores do Gráfico"
      >
        📊 {isExpanded ? 'Ocultar' : 'Editar Dados'}
      </button>

      {/* Controls Panel */}
      {isExpanded && (
        <div style={styles.panel}>
          {/* Header */}
          <div style={styles.header}>
            <h3 style={styles.title}>📊 Editar Alocação - {usuario.nome}</h3>
            <div style={styles.headerButtons}>
              <button onClick={applyChanges} style={styles.applyButton}>
                ✅ Aplicar
              </button>
              <button onClick={resetToOriginal} style={styles.resetButton}>
                🔄 Restaurar
              </button>
            </div>
          </div>

          {/* Total Display */}
          <div style={styles.totalSection}>
            <div style={styles.totalDisplay}>
              <strong>💰 Total: {formatCurrency(total)}</strong>
            </div>
            <button onClick={autoDistribute} style={styles.distributeButton}>
              ⚖️ Distribuir Igualmente
            </button>
          </div>

          {/* Tabs */}
          <div style={styles.tabs}>
            <button 
              onClick={() => setActiveTab('values')}
              style={{...styles.tab, ...(activeTab === 'values' ? styles.activeTab : {})}}
            >
              💰 Valores
            </button>
            <button 
              onClick={() => setActiveTab('percentages')}
              style={{...styles.tab, ...(activeTab === 'percentages' ? styles.activeTab : {})}}
            >
              📊 Percentuais
            </button>
          </div>

          {/* Tab Content */}
          <div style={styles.content}>
            {activeTab === 'values' && (
              <div style={styles.section}>
                <div style={styles.hint}>
                  💡 Arraste os valores para cima/baixo ou digite diretamente
                </div>
                {ativos.alocacao.map((item, index) => {
                  const percentage = total > 0 ? (item.value / total) * 100 : 0;
                  return (
                    <div key={index} style={styles.itemControl}>
                      <div style={styles.itemHeader}>
                        <span style={styles.itemName}>{item.product || item.nome}</span>
                        <span style={styles.itemPercentage}>{percentage.toFixed(1)}%</span>
                      </div>
                      <div style={styles.valueControl}>
                        <div
                          style={styles.dragHandle}
                          onMouseDown={(e) => handleMouseDown(index, e)}
                          title="Arraste para ajustar"
                        >
                          ↕️
                        </div>
                        <input
                          type="number"
                          value={item.value}
                          onChange={(e) => handleValueChange(index, e.target.value)}
                          style={styles.valueInput}
                          min="0"
                          step="1000"
                        />
                        <div style={styles.valueCurrency}>
                          {formatCurrency(item.value)}
                        </div>
                      </div>
                      <div style={styles.progressBar}>
                        <div 
                          style={{
                            ...styles.progressFill,
                            width: `${Math.min(100, percentage)}%`,
                            backgroundColor: getColorForIndex(index)
                          }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {activeTab === 'percentages' && (
              <div style={styles.section}>
                <div style={styles.hint}>
                  📊 Ajuste os percentuais (valores serão recalculados automaticamente)
                </div>
                {ativos.alocacao.map((item, index) => {
                  const percentage = total > 0 ? (item.value / total) * 100 : 0;
                  return (
                    <div key={index} style={styles.itemControl}>
                      <div style={styles.itemHeader}>
                        <span style={styles.itemName}>{item.product || item.nome}</span>
                        <span style={styles.itemValue}>{formatCurrency(item.value)}</span>
                      </div>
                      <div style={styles.percentageControl}>
                        <input
                          type="range"
                          min="0"
                          max="100"
                          step="0.1"
                          value={percentage}
                          onChange={(e) => handlePercentageChange(index, e.target.value)}
                          style={styles.percentageSlider}
                        />
                        <input
                          type="number"
                          value={percentage.toFixed(1)}
                          onChange={(e) => handlePercentageChange(index, e.target.value)}
                          style={styles.percentageInput}
                          min="0"
                          max="100"
                          step="0.1"
                        />
                        <span style={styles.percentageSymbol}>%</span>
                      </div>
                      <div style={styles.progressBar}>
                        <div 
                          style={{
                            ...styles.progressFill,
                            width: `${Math.min(100, percentage)}%`,
                            backgroundColor: getColorForIndex(index)
                          }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Status Bar */}
          <div style={styles.statusBar}>
            <small style={styles.statusText}>
              📊 {ativos.alocacao.length} ativos | 
              💰 Total: {formatCurrency(total)} | 
              📅 {new Date().toLocaleString()}
            </small>
          </div>
        </div>
      )}
    </div>
  );
}

// Helper function to get color for each asset
const getColorForIndex = (index) => {
  const colors = [
    "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
    "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
  ];
  return colors[index % colors.length];
};

// Styles
const styles = {
  container: {
    position: 'fixed',
    top: '60px',
    right: '10px',
    zIndex: 1000,
    fontFamily: 'Arial, sans-serif'
  },
  toggleButton: {
    padding: '8px 12px',
    backgroundColor: '#4CAF50',
    color: 'white',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '12px',
    fontWeight: 'bold',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
    transition: 'all 0.3s ease'
  },
  panel: {
    marginTop: '8px',
    backgroundColor: 'white',
    border: '1px solid #ddd',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
    minWidth: '380px',
    maxWidth: '450px',
    maxHeight: '70vh',
    overflowY: 'auto'
  },
  header: {
    padding: '12px',
    borderBottom: '1px solid #eee',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  title: {
    margin: 0,
    fontSize: '14px',
    color: '#333'
  },
  headerButtons: {
    display: 'flex',
    gap: '8px'
  },
  applyButton: {
    padding: '4px 8px',
    backgroundColor: '#4CAF50',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '10px'
  },
  resetButton: {
    padding: '4px 8px',
    backgroundColor: '#FF9800',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '10px'
  },
  totalSection: {
    padding: '12px',
    backgroundColor: '#f8f9fa',
    borderBottom: '1px solid #eee',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  totalDisplay: {
    fontSize: '14px',
    color: '#333'
  },
  distributeButton: {
    padding: '4px 8px',
    backgroundColor: '#2196F3',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '10px'
  },
  tabs: {
    display: 'flex',
    borderBottom: '1px solid #eee'
  },
  tab: {
    flex: 1,
    padding: '8px 12px',
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    fontSize: '11px',
    color: '#666',
    borderBottom: '2px solid transparent'
  },
  activeTab: {
    color: '#4CAF50',
    borderBottomColor: '#4CAF50',
    backgroundColor: '#f8f9fa'
  },
  content: {
    padding: '16px'
  },
  section: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px'
  },
  hint: {
    fontSize: '11px',
    color: '#666',
    fontStyle: 'italic',
    padding: '8px',
    backgroundColor: '#f0f8ff',
    borderRadius: '4px',
    border: '1px solid #e3f2fd'
  },
  itemControl: {
    padding: '12px',
    border: '1px solid #eee',
    borderRadius: '6px',
    backgroundColor: '#fafafa'
  },
  itemHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '8px'
  },
  itemName: {
    fontSize: '12px',
    fontWeight: 'bold',
    color: '#333'
  },
  itemPercentage: {
    fontSize: '11px',
    color: '#666',
    fontWeight: 'bold'
  },
  itemValue: {
    fontSize: '11px',
    color: '#666',
    fontWeight: 'bold'
  },
  valueControl: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    marginBottom: '8px'
  },
  dragHandle: {
    cursor: 'ns-resize',
    padding: '4px',
    backgroundColor: '#e0e0e0',
    borderRadius: '3px',
    fontSize: '10px',
    userSelect: 'none',
    minWidth: '20px',
    textAlign: 'center'
  },
  valueInput: {
    flex: 1,
    padding: '4px 8px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '11px',
    textAlign: 'right'
  },
  valueCurrency: {
    fontSize: '10px',
    color: '#888',
    minWidth: '80px',
    textAlign: 'right'
  },
  percentageControl: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    marginBottom: '8px'
  },
  percentageSlider: {
    flex: 1,
    height: '6px',
    borderRadius: '3px',
    outline: 'none',
    cursor: 'pointer'
  },
  percentageInput: {
    width: '60px',
    padding: '4px 6px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '11px',
    textAlign: 'right'
  },
  percentageSymbol: {
    fontSize: '11px',
    color: '#666'
  },
  progressBar: {
    height: '6px',
    backgroundColor: '#e0e0e0',
    borderRadius: '3px',
    overflow: 'hidden'
  },
  progressFill: {
    height: '100%',
    transition: 'width 0.3s ease'
  },
  statusBar: {
    padding: '8px 12px',
    backgroundColor: '#f8f9fa',
    borderTop: '1px solid #eee',
    borderRadius: '0 0 8px 8px'
  },
  statusText: {
    color: '#666',
    fontSize: '10px'
  }
};
