/**
 * Development Setup Guide for DGM Canvas History Integration
 */

console.log(`
🚀 INTEGRAÇÃO DE HISTÓRICO - GUIA DE CONFIGURAÇÃO
================================================

## 📋 REQUISITOS

1. ✅ app-calc-reino funcionando com Supabase
2. ✅ DGM Canvas executando (porta 5173)
3. 🔄 Servidor de API de histórico (porta 3001)

## 🔧 CONFIGURAÇÃO RÁPIDA

### Passo 1: Configurar API de Histórico no app-calc-reino

1. Instalar dependências (se necessário):
   npm install express cors

2. Criar servidor de desenvolvimento:
   
\`\`\`javascript
// dev-history-server.js
import express from 'express';
import cors from 'cors';
import { setupHistoryAPI } from './src/api/history-server-v2.js';

const app = express();
app.use(cors());
app.use(express.json());

// Setup history endpoints
const server = { middlewares: { use: app.use.bind(app) } };
setupHistoryAPI(server);

const PORT = 3001;
app.listen(PORT, () => {
  console.log(\`🚀 History API server running on http://localhost:\${PORT}\`);
  console.log(\`📊 Available endpoints:\`);
  console.log(\`   GET http://localhost:\${PORT}/api/history - Documentation\`);
  console.log(\`   GET http://localhost:\${PORT}/api/history/recent - Recent submissions\`);
  console.log(\`   GET http://localhost:\${PORT}/api/history/stats - Statistics\`);
});
\`\`\`

3. Executar servidor:
   node dev-history-server.js

### Passo 2: Testar DGM Canvas

1. Iniciar DGM Canvas:
   cd dgm-canvas
   npm run dev

2. Acessar: http://localhost:5173

3. Usar painel de controle de histórico (canto superior direito)

## 🎯 FUNCIONALIDADES DISPONÍVEIS

### No DGM Canvas:
- 📊 Carregar últimos 5/10 registros
- 📈 Ver estatísticas de submissões
- 🔍 Buscar por nome/email
- ⬅️➡️ Navegar entre registros históricos
- 🔄 Alternar entre modo ao vivo e histórico

### APIs Disponíveis:
- GET /api/history/recent?limit=10
- GET /api/history/stats  
- GET /api/history/search?q=joao&limit=20
- GET /api/history/date-range?startDate=2025-01-01&endDate=2025-01-31
- GET /api/history/:id

## 🧪 TESTES

### Teste 1: Conexão da API
\`\`\`javascript
// No console do DGM Canvas
const result = await window.dataManager.testHistoryConnection();
console.log('Conexão:', result);
\`\`\`

### Teste 2: Buscar dados recentes
\`\`\`javascript
const data = await window.dataManager.fetchRecentSubmissions(5);
console.log('Dados recentes:', data);
\`\`\`

### Teste 3: Estatísticas
\`\`\`javascript
const stats = await window.dataManager.fetchStats();
console.log('Estatísticas:', stats);
\`\`\`

## 🐛 TROUBLESHOOTING

### ❌ Erro: "History API connection failed"
- Verifique se o servidor de API está rodando na porta 3001
- Confirme que o Supabase está configurado no app-calc-reino
- Teste: curl http://localhost:3001/api/history

### ❌ Erro: "CORS"
- Certifique-se que o servidor Express tem cors() configurado
- Verifique se as origens estão permitidas

### ❌ Dados não aparecem
- Confirme que há dados no Supabase (tabela calculator_submissions)
- Teste as APIs diretamente no navegador
- Verifique logs no console

## 🎉 PRÓXIMOS PASSOS

1. ✅ Configurar produção com endpoints reais
2. ✅ Adicionar autenticação se necessário  
3. ✅ Implementar cache para performance
4. ✅ Adicionar mais visualizações (gráficos de linha, barras)
5. ✅ Exportar dados para Excel/PDF

## 📞 SUPORTE

Use os logs detalhados no console para debug:
- 📊 [DataManager] - Logs do gerenciador de dados
- 📈 [HistoryAPI] - Logs da API de histórico  
- 🎨 [HistoryControl] - Logs do painel de controle

---
💡 DICA: Use ?debug=true na URL para logs detalhados
`);

// If running in browser, make utilities available globally
if (typeof window !== "undefined") {
  window.DGMHistoryUtils = {
    testConnection: async () => {
      try {
        const response = await fetch("http://localhost:3001/api/history");
        const result = await response.json();
        console.log("✅ Connection test successful:", result);
        return result;
      } catch (error) {
        console.error("❌ Connection test failed:", error);
        return { error: error.message };
      }
    },

    fetchSample: async () => {
      try {
        const response = await fetch(
          "http://localhost:3001/api/history/recent?limit=3"
        );
        const result = await response.json();
        console.log("📊 Sample data:", result);
        return result;
      } catch (error) {
        console.error("❌ Fetch failed:", error);
        return { error: error.message };
      }
    },

    fetchStats: async () => {
      try {
        const response = await fetch("http://localhost:3001/api/history/stats");
        const result = await response.json();
        console.log("📈 Statistics:", result);
        return result;
      } catch (error) {
        console.error("❌ Stats fetch failed:", error);
        return { error: error.message };
      }
    },
  };

  console.log("🔧 Utils available at: window.DGMHistoryUtils");
  console.log("   - testConnection()");
  console.log("   - fetchSample()");
  console.log("   - fetchStats()");
}
