#!/bin/bash

# 🚀 Script de Deploy Automático - App Calc <PERSON>ino & DGM Canvas
# Versão: 1.0
# Última atualização: Janeiro 2025

set -e  # Exit on any error

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Função para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Verificar dependências
check_dependencies() {
    log "Verificando dependências..."
    
    if ! command -v node &> /dev/null; then
        error "Node.js não encontrado. Instale Node.js 18+ primeiro."
    fi
    
    if ! command -v pnpm &> /dev/null; then
        warning "pnpm não encontrado. Instalando..."
        npm install -g pnpm
    fi
    
    if ! command -v git &> /dev/null; then
        error "Git não encontrado. Instale Git primeiro."
    fi
    
    success "Dependências verificadas"
}

# Verificar variáveis de ambiente
check_env_vars() {
    log "Verificando variáveis de ambiente..."
    
    local required_vars=(
        "SUPABASE_URL"
        "SUPABASE_ANON_KEY"
        "SUPABASE_SERVICE_ROLE_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            error "Variável de ambiente $var não definida. Configure no arquivo .env"
        fi
    done
    
    success "Variáveis de ambiente verificadas"
}

# Deploy App-Calc-Reino
deploy_app_calc_reino() {
    log "Iniciando deploy do App-Calc-Reino..."
    
    cd app-calc-reino || error "Diretório app-calc-reino não encontrado"
    
    # Instalar dependências
    log "Instalando dependências..."
    pnpm install --frozen-lockfile
    
    # Executar testes se existirem
    if [ -f "package.json" ] && grep -q "\"test\":" package.json; then
        log "Executando testes..."
        pnpm test || warning "Testes falharam, continuando deploy..."
    fi
    
    # Build de produção
    log "Criando build de produção..."
    pnpm run build
    
    # Verificar se build foi criado
    if [ ! -d "dist" ]; then
        error "Build não foi criado. Verifique o comando de build."
    fi
    
    success "App-Calc-Reino pronto para deploy"
    cd ..
}

# Deploy DGM Canvas
deploy_dgm_canvas() {
    log "Iniciando deploy do DGM Canvas..."
    
    cd dgm-canvas || error "Diretório dgm-canvas não encontrado"
    
    # Instalar dependências
    log "Instalando dependências..."
    npm ci
    
    # Executar testes se existirem
    if [ -f "package.json" ] && grep -q "\"test\":" package.json; then
        log "Executando testes..."
        npm test || warning "Testes falharam, continuando deploy..."
    fi
    
    # Build de produção
    log "Criando build de produção..."
    npm run build
    
    # Verificar se build foi criado
    if [ ! -d "dist" ]; then
        error "Build não foi criado. Verifique o comando de build."
    fi
    
    success "DGM Canvas pronto para deploy"
    cd ..
}

# Verificar conectividade com Supabase
test_supabase_connection() {
    log "Testando conexão com Supabase..."
    
    if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_ANON_KEY" ]; then
        warning "Credenciais do Supabase não configuradas, pulando teste de conexão"
        return
    fi
    
    # Teste simples de conexão via curl
    response=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "apikey: $SUPABASE_ANON_KEY" \
        -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
        "$SUPABASE_URL/rest/v1/")
    
    if [ "$response" = "200" ]; then
        success "Conexão com Supabase OK"
    else
        warning "Falha na conexão com Supabase (HTTP $response)"
    fi
}

# Deploy para Vercel (DGM Canvas)
deploy_to_vercel() {
    log "Fazendo deploy para Vercel..."
    
    if ! command -v vercel &> /dev/null; then
        log "Instalando Vercel CLI..."
        npm install -g vercel
    fi
    
    cd dgm-canvas || error "Diretório dgm-canvas não encontrado"
    
    # Deploy para produção
    vercel --prod --confirm
    
    success "Deploy para Vercel concluído"
    cd ..
}

# Deploy para Render (App-Calc-Reino)
deploy_to_render() {
    log "Instruções para deploy no Render:"
    echo ""
    echo "1. Acesse https://render.com e conecte seu repositório"
    echo "2. Configure as seguintes variáveis de ambiente:"
    echo "   - NODE_ENV=production"
    echo "   - SUPABASE_URL=$SUPABASE_URL"
    echo "   - SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY"
    echo "   - SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY"
    echo "3. Build Command: pnpm install && pnpm run build"
    echo "4. Start Command: pnpm run serve"
    echo ""
    success "Instruções para Render exibidas"
}

# Criar arquivos de configuração necessários
create_config_files() {
    log "Criando arquivos de configuração..."
    
    # Dockerfile para app-calc-reino
    if [ ! -f "app-calc-reino/Dockerfile" ]; then
        cat > app-calc-reino/Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Instalar pnpm
RUN npm install -g pnpm

# Copiar arquivos de dependências
COPY package.json pnpm-lock.yaml ./

# Instalar dependências
RUN pnpm install --frozen-lockfile

# Copiar código fonte
COPY . .

# Build da aplicação
RUN pnpm run build

# Expor porta
EXPOSE 3000

# Comando de inicialização
CMD ["pnpm", "run", "serve"]
EOF
        success "Dockerfile criado para app-calc-reino"
    fi
    
    # vercel.json para dgm-canvas
    if [ ! -f "dgm-canvas/vercel.json" ]; then
        cat > dgm-canvas/vercel.json << 'EOF'
{
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "api/**/*.js": {
      "runtime": "nodejs18.x"
    }
  },
  "rewrites": [
    {
      "source": "/api/:path*",
      "destination": "/api/:path*"
    }
  ]
}
EOF
        success "vercel.json criado para dgm-canvas"
    fi
    
    # netlify.toml para dgm-canvas (alternativa)
    if [ ! -f "dgm-canvas/netlify.toml" ]; then
        cat > dgm-canvas/netlify.toml << 'EOF'
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
EOF
        success "netlify.toml criado para dgm-canvas"
    fi
}

# Função principal
main() {
    echo ""
    echo "🚀 Deploy Automático - App Calc Reino & DGM Canvas"
    echo "=================================================="
    echo ""
    
    # Verificar se estamos no diretório correto
    if [ ! -d "app-calc-reino" ] || [ ! -d "dgm-canvas" ]; then
        error "Execute este script na raiz do workspace (onde estão as pastas app-calc-reino e dgm-canvas)"
    fi
    
    # Carregar variáveis de ambiente se existir arquivo .env
    if [ -f ".env" ]; then
        log "Carregando variáveis de ambiente do arquivo .env"
        export $(cat .env | xargs)
    fi
    
    check_dependencies
    check_env_vars
    create_config_files
    test_supabase_connection
    
    # Menu de opções
    echo ""
    echo "Escolha uma opção de deploy:"
    echo "1) Build local apenas (preparar para deploy)"
    echo "2) Deploy DGM Canvas para Vercel"
    echo "3) Instruções para deploy App-Calc-Reino no Render"
    echo "4) Build completo (ambos os projetos)"
    echo "5) Sair"
    echo ""
    read -p "Digite sua opção (1-5): " choice
    
    case $choice in
        1)
            deploy_app_calc_reino
            deploy_dgm_canvas
            success "Build local concluído para ambos os projetos"
            ;;
        2)
            deploy_dgm_canvas
            deploy_to_vercel
            ;;
        3)
            deploy_app_calc_reino
            deploy_to_render
            ;;
        4)
            deploy_app_calc_reino
            deploy_dgm_canvas
            success "Build completo concluído"
            ;;
        5)
            log "Saindo..."
            exit 0
            ;;
        *)
            error "Opção inválida"
            ;;
    esac
    
    echo ""
    success "Deploy script concluído!"
    echo ""
    echo "📋 Próximos passos:"
    echo "- Verifique os logs de deploy das plataformas"
    echo "- Configure DNS se necessário"
    echo "- Teste as aplicações em produção"
    echo "- Configure monitoramento"
    echo ""
}

# Executar função principal
main "$@"
