import { useRef, useEffect } from "react";
import { Pie<PERSON><PERSON> } from "../charts/PieChart.js";
import { D3PieChartBridge } from "../charts/D3PieChartBridge.js";

export const useDGMCanvas = (dataManager) => {
  const editorRef = useRef(null);
  const pieChartRef = useRef(null);
  const d3PieChartRef = useRef(null);

  const handleMount = async (editor) => {
    editorRef.current = editor;
    editor.newDoc();
    editor.fitToScreen();

    window.addEventListener("resize", () => {
      editor.fit();
    });

    pieChartRef.current = new Pie<PERSON>hart(editor, {
      centerX: 400,
      centerY: 350,
      radius: 120,
    });

    // Initialize D3.js Hybrid Chart
    d3PieChartRef.current = new D3PieChartBridge(editor, {
      centerX: 400,
      centerY: 350,
      radius: 120,
      padAngle: 0.02,
      cornerRadius: 3,
    });

    console.log("✅ [useDGMCanvas] Both chart engines initialized");
    console.log("📊 Original PieChart:", pieChartRef.current);
    console.log("🎨 D3 Hybrid Bridge:", d3PieChartRef.current);

    // Check if dataManager already has data and create chart automatically
    if (dataManager?.currentData) {
      setTimeout(() => {
        updateCanvasWithData(dataManager.currentData);
      }, 100);
    }

    if (window.location.search.includes("test=true") && dataManager) {
      dataManager.loadTestData();
    }
  };

  const updateCanvasWithData = (data) => {
    if (!editorRef.current || !data) {
      return;
    }

    const editor = editorRef.current;

    try {
      editor.newDoc();
      createVisualizationHeader(editor, data);

      // Use D3.js Hybrid Bridge instead of original PieChart
      if (d3PieChartRef.current) {
        console.log("🎨 [useDGMCanvas] Using D3.js Hybrid Bridge");
        d3PieChartRef.current.create(
          data.ativos.alocacao,
          data.patrimonio.total
        );
      } else if (pieChartRef.current) {
        console.log("📊 [useDGMCanvas] Fallback to original PieChart");
        pieChartRef.current.create(data.ativos.alocacao, data.patrimonio.total);
      }

      createTimestamp(editor, data);
      editor.fitToScreen();
    } catch (error) {
      console.error("❌ Error updating canvas:", error);
    }
  };

  const recreateChartWithCurrentData = (dataManager) => {
    if (dataManager?.currentData) {
      updateCanvasWithData(dataManager.currentData);
    }
  };

  const createVisualizationHeader = (editor, data) => {
    const { patrimonio, usuario } = data;

    try {
      const titleRect = [
        [50, 30],
        [400, 60],
      ];
      const title = editor.factory.createText(
        titleRect,
        `Patrimônio de ${usuario.nome || "Usuário"}`
      );
      title.fontSize = 24;
      title.fontWeight = "bold";
      title.fillColor = "#333";

      editor.actions.insert(title);

      const patrimonioText = [
        `💰 Total: R$ ${patrimonio.total.toLocaleString("pt-BR")}`,
        `📊 Alocado: R$ ${patrimonio.alocado.toLocaleString("pt-BR")} (${
          patrimonio.percentualAlocado
        }%)`,
        `💵 Restante: R$ ${patrimonio.restante.toLocaleString("pt-BR")}`,
      ].join("\n");

      const infoRect = [
        [50, 80],
        [500, 150],
      ];
      const info = editor.factory.createText(infoRect, patrimonioText);
      info.fontSize = 14;
      info.fillColor = "#666";

      editor.actions.insert(info);
    } catch (error) {
      console.error(
        "❌ [useDGMCanvas] Error creating visualization header:",
        error
      );
    }
  };

  const createTimestamp = (editor, data) => {
    try {
      const timestampRect = [
        [50, 550],
        [400, 570],
      ];
      const timestamp = editor.factory.createText(
        timestampRect,
        `Atualizado em: ${new Date(data.timestamp).toLocaleString("pt-BR")}`
      );
      timestamp.fontSize = 10;
      timestamp.fillColor = "#666";

      editor.actions.insert(timestamp);
    } catch (error) {
      console.error("❌ [useDGMCanvas] Error creating timestamp:", error);
    }
  };

  useEffect(() => {
    if (!dataManager) return;

    const handleDataReceived = ({ data }) => {
      // Only update canvas if all components are ready
      if (pieChartRef.current && editorRef.current) {
        updateCanvasWithData(data);
      } else {
        // Retry after a short delay
        setTimeout(() => {
          if (pieChartRef.current && editorRef.current) {
            updateCanvasWithData(data);
          }
        }, 200);
      }
    };

    dataManager.addEventListener("dataReceived", handleDataReceived);

    return () => {
      dataManager.removeEventListener("dataReceived", handleDataReceived);
    };
  }, [dataManager]);

  return {
    handleMount,
    updateCanvasWithData,
    recreateChartWithCurrentData,
    pieChart: pieChartRef.current,
    d3PieChart: d3PieChartRef.current,
    editor: editorRef.current,
  };
};
