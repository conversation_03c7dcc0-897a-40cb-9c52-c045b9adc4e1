import { existsSync, statSync, readFileSync } from 'fs';
import { join } from 'path';
import { createHash } from 'crypto';

// Configuration
const WEBFLOW_FOLDER = 'Modelo - Webflow';
const POLICY_FILE = 'WEBFLOW_POLICY.md';
const WEBFLOW_README = join(WEBFLOW_FOLDER, 'README.md');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

/**
 * Logs a colored message to console
 * @param {string} message - The message to log
 * @param {string} color - The color from colors object
 */
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Logs a header with decorative formatting
 * @param {string} title - The title to display
 */
function logHeader(title) {
  const border = '='.repeat(title.length + 4);
  log(border, 'cyan');
  log(`  ${title}  `, 'cyan');
  log(border, 'cyan');
}

/**
 * Calculates MD5 hash of a file
 * @param {string} filePath - Path to the file
 * @returns {string} - MD5 hash
 */
function getFileHash(filePath) {
  try {
    const content = readFileSync(filePath);
    return createHash('md5').update(content).digest('hex');
  } catch (error) {
    return null;
  }
}

/**
 * Checks if required policy files exist
 * @returns {boolean} - True if all policy files exist
 */
function validatePolicyFiles() {
  logHeader('POLICY FILES VALIDATION');

  let allValid = true;

  // Check main policy file
  if (existsSync(POLICY_FILE)) {
    log('✅ Main policy file exists: WEBFLOW_POLICY.md', 'green');
  } else {
    log('❌ Missing main policy file: WEBFLOW_POLICY.md', 'red');
    allValid = false;
  }

  // Check Webflow folder README
  if (existsSync(WEBFLOW_README)) {
    log('✅ Webflow folder README exists', 'green');
  } else {
    log('❌ Missing Webflow folder README.md', 'red');
    allValid = false;
  }

  return allValid;
}

/**
 * Validates the Webflow folder structure
 * @returns {boolean} - True if structure is valid
 */
function validateWebflowStructure() {
  logHeader('WEBFLOW FOLDER STRUCTURE VALIDATION');

  if (!existsSync(WEBFLOW_FOLDER)) {
    log(`❌ Webflow folder not found: ${WEBFLOW_FOLDER}`, 'red');
    return false;
  }

  log(`✅ Webflow folder exists: ${WEBFLOW_FOLDER}`, 'green');

  // Expected files and folders
  const expectedItems = [
    { path: 'index.html', type: 'file', required: true },
    { path: 'untitled.html', type: 'file', required: false },
    { path: 'css', type: 'directory', required: true },
    { path: 'fonts', type: 'directory', required: false },
    { path: 'images', type: 'directory', required: false },
    { path: 'js', type: 'directory', required: false },
    { path: 'README.md', type: 'file', required: true }
  ];

  let structureValid = true;

  for (const item of expectedItems) {
    const fullPath = join(WEBFLOW_FOLDER, item.path);

    if (existsSync(fullPath)) {
      const stats = statSync(fullPath);
      const actualType = stats.isDirectory() ? 'directory' : 'file';

      if (actualType === item.type) {
        log(`✅ ${item.path} (${item.type})`, 'green');
      } else {
        log(`❌ ${item.path} - Expected ${item.type}, found ${actualType}`, 'red');
        structureValid = false;
      }
    } else {
      if (item.required) {
        log(`❌ Missing required ${item.type}: ${item.path}`, 'red');
        structureValid = false;
      } else {
        log(`⚠️  Optional ${item.type} not found: ${item.path}`, 'yellow');
      }
    }
  }

  return structureValid;
}

/**
 * Warns about potential violations
 */
function checkForViolations() {
  logHeader('POTENTIAL VIOLATIONS CHECK');

  // Check for common violation patterns
  const violations = [];

  // Check if there are any unexpected files in the Webflow folder
  // This is a basic check - in a real scenario, you might want to compare against
  // a baseline or check file timestamps

  log('ℹ️  This validation checks basic structure and policy compliance', 'blue');
  log('ℹ️  For change detection, use git status or git diff', 'blue');

  // Reminder about proper workflow
  log('', 'reset');
  log('📋 PROPER WORKFLOW REMINDER:', 'bright');
  log('1. Make changes in Webflow platform', 'reset');
  log('2. Export files from Webflow', 'reset');
  log('3. Replace entire Modelo - Webflow folder', 'reset');
  log('4. Commit the replacement as a single operation', 'reset');
}

/**
 * Displays policy reminders
 */
function displayPolicyReminders() {
  logHeader('POLICY REMINDERS');

  log('🚨 CRITICAL RULES:', 'red');
  log('   • NEVER edit files in Modelo - Webflow directly', 'red');
  log('   • ALWAYS use Webflow platform for changes', 'red');
  log('   • ONLY replace entire folder with exports', 'red');

  log('', 'reset');
  log('✅ ALLOWED ACTIONS:', 'green');
  log('   • Read files for reference', 'green');
  log('   • Copy code snippets for analysis', 'green');
  log('   • Examine HTML structure', 'green');

  log('', 'reset');
  log('📚 DOCUMENTATION:', 'blue');
  log(`   • Main policy: ${POLICY_FILE}`, 'blue');
  log(`   • Folder README: ${WEBFLOW_README}`, 'blue');
}

/**
 * Main validation function
 */
function main() {
  log('🔍 WEBFLOW TEMPLATE FOLDER VALIDATION', 'bright');
  log('', 'reset');

  const results = {
    policyFiles: validatePolicyFiles(),
    structure: validateWebflowStructure()
  };

  log('', 'reset');
  checkForViolations();

  log('', 'reset');
  displayPolicyReminders();

  // Final summary
  log('', 'reset');
  logHeader('VALIDATION SUMMARY');

  if (results.policyFiles && results.structure) {
    log('✅ All validations passed!', 'green');
    log('✅ Webflow template folder policy is properly configured', 'green');
  } else {
    log('❌ Some validations failed', 'red');
    log('❌ Please address the issues above', 'red');
    process.exit(1);
  }

  log('', 'reset');
  log('🔗 Remember: Webflow platform is the source of truth!', 'cyan');
}

// Run validation
main();
