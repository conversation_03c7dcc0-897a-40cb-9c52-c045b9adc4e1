/**
 * D3PieChartBridge - Híbrido D3.js + DGM Canvas
 *
 * Esta classe usa D3.js para cálculos precisos de gráficos de torta
 * e DGM Canvas para renderização e interação.
 *
 * Vantagens:
 * ✅ Cálculos precisos do D3.js
 * ✅ Interação rica do DGM Canvas
 * ✅ Implementação simplificada e limpa
 */

import * as d3 from "d3";

export class D3PieChartBridge {
  constructor(editor, config = {}) {
    this.editor = editor;
    this.config = {
      centerX: config.centerX || 400,
      centerY: config.centerY || 350,
      radius: config.radius || 120,
      colors: config.colors || [
        "#FF6B6B",
        "#4ECDC4",
        "#45B7D1",
        "#96CEB4",
        "#FFEAA7",
        "#DDA0DD",
        "#98D8C8",
        "#F7DC6F",
        "#BB8FCE",
        "#85C1E9",
      ],
      ...config,
    };

    this.elements = [];
    this.pieData = [];

    // Inicializar D3.js generators
    this.initD3Generators();
  }

  /**
   * Inicializa os geradores D3.js para pie e arc
   */
  initD3Generators() {
    // D3.js Pie Generator - Calcula ângulos
    this.pie = d3
      .pie()
      .value((d) => d.value)
      .sort(null); // Manter ordem original dos dados

    // D3.js Arc Generator - Gera paths
    this.arc = d3
      .arc()
      .innerRadius(0)
      .outerRadius(parseFloat(this.config.radius) || 120);

    console.log("✅ [D3PieChartBridge] D3.js generators initialized");
  }

  /**
   * Cria o gráfico usando D3.js para cálculos e DGM para renderização
   */
  create(alocacao, total) {
    this.clear();

    if (!this.validateData(alocacao, total)) return;

    // 1. Preparar dados para D3.js
    const data = this.prepareData(alocacao, total);

    // 2. Usar D3.js para calcular ângulos e posições
    this.pieData = this.pie(data);

    // 3. Criar elementos DGM baseados nos cálculos D3.js
    this.createDGMElements();

    console.log("✅ [D3PieChartBridge] Chart created successfully");
    console.log("📊 Pie data:", this.pieData);
  }

  /**
   * Prepara os dados no formato esperado pelo D3.js
   */
  prepareData(alocacao, total) {
    console.log("🔢 [D3PieChartBridge] Preparing data:", { alocacao, total });

    const data = alocacao.map((ativo, index) => {
      const value = parseFloat(ativo.value) || 0;
      const percentage = total > 0 ? (value / total) * 100 : 0;

      const item = {
        name: ativo.nome || `Ativo ${index + 1}`,
        value: value,
        percentage: percentage,
        originalData: ativo,
        index,
      };

      console.log(`📊 [D3PieChartBridge] Item ${index}:`, item);
      return item;
    });

    // Validar se a soma das porcentagens está correta
    const totalPercentage = data.reduce(
      (sum, item) => sum + item.percentage,
      0
    );
    console.log(
      `🧮 [D3PieChartBridge] Total percentage: ${totalPercentage.toFixed(2)}%`
    );

    if (Math.abs(totalPercentage - 100) > 0.1) {
      console.warn(
        `⚠️ [D3PieChartBridge] Percentage sum mismatch: ${totalPercentage.toFixed(
          2
        )}% (expected: 100%)`
      );
    }

    console.log("✅ [D3PieChartBridge] Data prepared:", data);
    return data;
  }

  /**
   * Cria elementos DGM baseados nos cálculos D3.js
   */
  createDGMElements() {
    console.log(
      "🎨 [D3PieChartBridge] Creating DGM elements for",
      this.pieData.length,
      "slices"
    );

    // Criar fatias, labels e legendas
    this.pieData.forEach((d, index) => {
      console.log(`🍰 [D3PieChartBridge] Creating slice ${index}:`, {
        startAngle: ((d.startAngle * 180) / Math.PI).toFixed(1) + "°",
        endAngle: ((d.endAngle * 180) / Math.PI).toFixed(1) + "°",
        value: d.value,
        percentage: d.data.percentage.toFixed(1) + "%",
      });

      this.createSlice(d, index);
      this.createLabel(d, index);
      this.createLegendEntry(d, index);
    });
  }

  /**
   * Cria uma fatia do gráfico usando dados calculados pelo D3.js
   */
  createSlice(pieSlice, index) {
    const { centerX, centerY, colors } = this.config;

    // Converter path SVG para pontos que o DGM pode usar
    const points = this.convertSVGPathToPoints(centerX, centerY, pieSlice);

    // Criar linha fechada DGM
    const slice = this.editor.factory.createLine(points, true);

    // Aplicar estilo
    slice.fillColor = colors[index % colors.length];
    slice.strokeColor = "#FFFFFF";
    slice.strokeWidth = 2;
    slice.movable = true;
    slice.selectable = true;

    // Metadados para interação
    slice.userData = {
      type: "pie-slice",
      data: pieSlice.data,
      startAngle: pieSlice.startAngle,
      endAngle: pieSlice.endAngle,
      value: pieSlice.value,
      percentage: pieSlice.data.percentage,
    };

    this.editor.actions.insert(slice);
    this.elements.push(slice);

    return slice;
  }

  /**
   * Converte ângulos D3.js para pontos que o DGM pode usar
   */
  convertSVGPathToPoints(centerX, centerY, pieSlice) {
    const { startAngle, endAngle } = pieSlice;
    const radius = parseFloat(this.config.radius) || 120;
    const points = [];

    // Começar do centro
    points.push([centerX, centerY]);

    // Número de pontos para aproximar o arco
    const sliceAngle = endAngle - startAngle;
    const numPoints = Math.max(8, Math.ceil((sliceAngle / (2 * Math.PI)) * 32));

    // Pontos do arco externo
    for (let i = 0; i <= numPoints; i++) {
      const angle = startAngle + (i * sliceAngle) / numPoints;
      // Ajustar ângulo para sistema de coordenadas do DGM Canvas
      const adjustedAngle = angle - Math.PI / 2;
      const x = centerX + radius * Math.cos(adjustedAngle);
      const y = centerY + radius * Math.sin(adjustedAngle);
      points.push([x, y]);
    }

    // Fechar no centro
    points.push([centerX, centerY]);

    return points;
  }
  /**
   * Cria label para uma fatia do gráfico
   */
  createLabel(pieSlice, index) {
    const { centerX, centerY } = this.config;

    // Calcular posição usando ângulo médio da fatia
    const midAngle = (pieSlice.startAngle + pieSlice.endAngle) / 2;
    const radius = parseFloat(this.config.radius) || 120;
    const labelRadius = radius * 0.7; // 70% do raio para posicionar os labels

    // Ajustar ângulo para sistema de coordenadas do DGM Canvas
    const adjustedAngle = midAngle - Math.PI / 2;

    const labelX = centerX + labelRadius * Math.cos(adjustedAngle);
    const labelY = centerY + labelRadius * Math.sin(adjustedAngle);

    // Formatar texto
    const percentage = pieSlice.data.percentage.toFixed(1);
    const name = pieSlice.data.name;
    const labelText = `${percentage}%`;

    // Criar label DGM
    const textWidth = Math.max(labelText.length * 8, 40);
    const textHeight = 16;

    const labelRect = [
      [labelX - textWidth / 2, labelY - textHeight / 2],
      [labelX + textWidth / 2, labelY + textHeight / 2],
    ];

    const label = this.editor.factory.createText(labelRect, labelText);

    // Configurações do texto
    label.fontSize = 13;
    label.fontWeight = "bold";
    label.fillColor = "#FFFFFF";
    label.strokeColor = "#000000";
    label.strokeWidth = 0.5;
    label.textAlign = "center";
    label.verticalAlign = "middle";
    label.movable = true;
    label.selectable = true;

    // Metadados
    label.userData = {
      type: "pie-label",
      parentSlice: index,
      data: pieSlice.data,
      midAngle: midAngle,
      originalPosition: [labelX, labelY],
    };

    this.editor.actions.insert(label);
    this.elements.push(label);

    console.log(
      `📝 [D3PieChartBridge] Label created for ${name}: ${percentage}% at [${labelX.toFixed(
        1
      )}, ${labelY.toFixed(1)}]`
    );

    return label;
  }

  /**
   * Cria entrada da legenda para uma fatia
   */
  createLegendEntry(pieSlice, index) {
    const { colors } = this.config;
    const { name, originalData } = pieSlice.data;
    const legendY = 200 + index * 25;

    try {
      // Quadrado colorido da legenda
      const legendColorRect = [
        [650, legendY],
        [670, legendY + 15],
      ];

      const legendColor = this.editor.factory.createRectangle(legendColorRect);
      legendColor.fillColor = colors[index % colors.length];
      legendColor.strokeColor = "#333";
      legendColor.movable = true;
      legendColor.selectable = true;

      this.editor.actions.insert(legendColor);
      this.elements.push(legendColor);

      // Texto da legenda
      const textRect = [
        [680, legendY],
        [880, legendY + 15],
      ];

      // Usar dados do originalData se disponível
      const product = originalData?.product || name;
      const valueFormatted =
        originalData?.valueFormatted ||
        `R$ ${pieSlice.value.toLocaleString("pt-BR")}`;
      const legendText = this.editor.factory.createText(
        textRect,
        `${product}: ${valueFormatted}`
      );

      legendText.fontSize = 12;
      legendText.fillColor = "#333";
      legendText.movable = true;
      legendText.selectable = true;

      this.editor.actions.insert(legendText);
      this.elements.push(legendText);

      console.log(`📋 [D3PieChartBridge] Legend entry created for ${product}`);

      return { color: legendColor, text: legendText };
    } catch (error) {
      console.error(
        "❌ [D3PieChartBridge] Error creating legend entry:",
        error
      );
      return null;
    }
  }

  /**
   * Valida os dados de entrada
   */
  validateData(alocacao, total) {
    if (!alocacao || !Array.isArray(alocacao) || alocacao.length === 0) {
      console.error("❌ [D3PieChartBridge] Invalid alocacao data:", alocacao);
      return false;
    }

    if (!total || total <= 0) {
      console.error("❌ [D3PieChartBridge] Invalid total value:", total);
      return false;
    }

    return true;
  }

  /**
   * Limpa elementos anteriores
   */
  clear() {
    this.elements = [];
    this.pieData = [];
  }

  /**
   * Atualiza configuração e recria gráfico
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.initD3Generators();
    console.log("✅ [D3PieChartBridge] Config updated");
  }

  /**
   * Obtém dados atuais do gráfico
   */
  getData() {
    return {
      pieData: this.pieData,
      elements: this.elements,
      config: this.config,
    };
  }
}
