# 🏗️ Architecture Documentation

This folder contains system architecture documentation and modular structure guides.

## 📋 Contents

### System Architecture

- **[MODULAR_STRUCTURE.md](./MODULAR_STRUCTURE.md)** - Complete modular architecture documentation
- **[MODULAR-README.md](./MODULAR-README.md)** - Modular system overview and guidelines

## 🏛️ Architecture Overview

The project follows a modular architecture with clear separation of concerns:

- **Frontend**: React-based UI components
- **Backend**: Node.js with Supabase integration  
- **Database**: PostgreSQL via Supabase
- **Integrations**: Typebot, Salesforce, DGM Canvas
- **Templates**: Webflow-generated HTML/CSS

## 📐 Key Principles

1. **Modularity** - Clear separation between components
2. **Scalability** - Easy to add new features and integrations
3. **Maintainability** - Well-documented and organized code
4. **Flexibility** - Support for multiple deployment scenarios

## 📚 Related Documentation

- [Integrations/](../Integrations/) - External service integrations
- [Setup/](../Setup/) - Initial setup and configuration
- [Development/](../Development/) - Development guidelines
