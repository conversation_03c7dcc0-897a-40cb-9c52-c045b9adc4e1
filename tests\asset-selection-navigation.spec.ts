import { test } from '@playwright/test';

/**
 * Teste para verificar seleção de ativos e navegação para alocação
 */
test.describe('Asset Selection and Navigation Test', () => {
  test('should allow navigation to allocation section after selecting assets', async ({ page }) => {
    // Load local file
    const indexPath = `file:///c:\\Users\\<USER>\\Desktop\\Integração Reino\\app-calc-reino\\Modelo - Webflow\\index.html`;
    await page.goto(indexPath);
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(3000); // Wait for all scripts to initialize

    // Step 1: Navigate to assets section
    const nextButtons = await page.locator('[if-element="button-next"]').all();
    if (nextButtons.length > 0) {
      // Click first next button (should go to money section)
      await nextButtons[0].click();
      await page.waitForTimeout(1000);

      // Click second next button if available (should go to assets section)
      if (nextButtons.length > 1) {
        await nextButtons[1].click();
        await page.waitForTimeout(1000);
      }
    } else {
      // Try navigation through step indicators
      const assetsStepIndicator = page.locator('[data-step="2"]').first();
      if (await assetsStepIndicator.isVisible()) {
        await assetsStepIndicator.click();
        await page.waitForTimeout(1000);
      }
    }

    // Step 2: Check if we're on the assets section
    const assetsSection = page.locator('._2-section-calc-ativos');
    const isAssetsVisible = await assetsSection.isVisible();

    if (!isAssetsVisible) {
      return;
    }

    // Step 3: Find and select assets
    const dropdownItems = await page.locator('.ativo-item-subcategory').all();
    const directItems = await page.locator('.ativos_item[ativo-product]').all();

    // Try to select dropdown items first
    for (let i = 0; i < Math.min(2, dropdownItems.length); i += 1) {
      try {
        await dropdownItems[i].click();
        await page.waitForTimeout(500);
      } catch {
        // Continue with next item
      }
    }

    // Try to select direct items
    for (let i = 0; i < Math.min(2, directItems.length); i += 1) {
      try {
        await directItems[i].click();
        await page.waitForTimeout(500);
      } catch {
        // Continue with next item
      }
    }

    // Step 4: Check asset selection state
    const selectionState = await page.evaluate(() => {
      const checkboxes = document.querySelectorAll('.asset-checkbox');
      const selectedAssets = document.querySelectorAll('.asset-checkbox:checked');
      const counter = document.querySelector('.counter_ativos');

      return {
        totalCheckboxes: checkboxes.length,
        selectedAssets: selectedAssets.length,
        counterText: counter?.textContent || 'N/A',
        selectedCategories: Array.from(selectedAssets).map((cb) => ({
          category: cb.getAttribute('data-category'),
          product: cb.getAttribute('data-product'),
        })),
      };
    });

    // Step 5: Try to navigate to allocation section
    const nextButton = page.locator('[if-element="button-next"]').last();

    try {
      await nextButton.click();
      await page.waitForTimeout(1500);

      const allocationSection = page.locator('._3-section-patrimonio-alocation');
      const isAllocationVisible = await allocationSection.isVisible();

      if (isAllocationVisible) {
        // Check filtered items in allocation section
        await page.evaluate(() => {
          const allocationItems = document.querySelectorAll(
            '._3-section-patrimonio-alocation [ativo-category][ativo-product]'
          );
          const visibleItems = Array.from(allocationItems).filter((item) => {
            const styles = getComputedStyle(item);
            return styles.display !== 'none';
          });

          return {
            totalItems: allocationItems.length,
            visibleItems: visibleItems.length,
            visibleProducts: visibleItems.map((item) => ({
              category: item.getAttribute('ativo-category'),
              product: item.getAttribute('ativo-product'),
            })),
          };
        });
      }
    } catch (error) {
      // Log error for debugging
      console.error('Navigation failed:', error);
    }

    // Take screenshot for visual debugging
    await page.screenshot({
      path: 'test-results/asset-selection-debug.png',
      fullPage: true,
    });

    // Return useful debugging information
    const debugInfo = await page.evaluate(() => {
      const globalWindow = window as any; // eslint-disable-line @typescript-eslint/no-explicit-any
      return {
        currentURL: window.location.href,
        modules: Object.keys(globalWindow.ReinoCalculator?.systems || {}),
        stepNavigation: {
          currentStep: globalWindow.ReinoCalculator?.systems?.stepNavigation?.currentStep,
          totalSteps: globalWindow.ReinoCalculator?.systems?.stepNavigation?.steps?.length,
        },
        assetSelection: {
          filterLoaded: Boolean(globalWindow.ReinoCalculator?.systems?.assetSelectionFilter),
          selectedCount: selectionState.selectedAssets,
        },
      };
    });

    return debugInfo;
  });
});
