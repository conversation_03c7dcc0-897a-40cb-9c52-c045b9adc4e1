{"name": "deployment-scripts", "version": "1.0.0", "description": "Scripts de deploy para App Calc Reino e DGM Canvas", "scripts": {"deploy:help": "echo 'Scripts de deploy disponíveis:' && echo '  npm run deploy:build - Build local de ambos os projetos' && echo '  npm run deploy:vercel - Deploy DGM Canvas para Vercel' && echo '  npm run deploy:render - Instruções para deploy no Render' && echo '  npm run deploy:config - Criar arquivos de configuração'", "deploy:build": "node scripts/deploy.js build", "deploy:vercel": "node scripts/deploy.js vercel", "deploy:render": "node scripts/deploy.js render", "deploy:config": "node scripts/deploy.js config", "deploy:check": "node scripts/deploy.js check", "pre-deploy": "npm run deploy:check && npm run deploy:build"}, "engines": {"node": ">=18.0.0"}}