/**
 * Salesforce Integration Configuration
 * Handles authentication and API connection with Salesforce
 */

// Salesforce Configuration
const SALESFORCE_CONFIG = {
  // Sandbox or Production
  INSTANCE_URL: 'https://your-org.my.salesforce.com',
  CLIENT_ID: 'your_connected_app_client_id',
  CLIENT_SECRET: 'your_connected_app_client_secret',
  USERNAME: '<EMAIL>',
  PASSWORD: 'your_password',
  SECURITY_TOKEN: 'your_security_token',

  // API Configuration
  API_VERSION: '58.0',
  TIMEOUT: 30000, // 30 seconds

  // Object Mapping
  OBJECTS: {
    LEAD: 'Lead',
    OPPORTUNITY: 'Opportunity',
    ACCOUNT: 'Account',
    CONTACT: 'Contact',
    CALCULATOR_SUBMISSION: 'Calculator_Submission__c', // Custom object
  },
};

/**
 * Salesforce Authentication Client
 */
class SalesforceAuth {
  constructor() {
    this.accessToken = null;
    this.instanceUrl = null;
    this.tokenExpiry = null;
  }

  /**
   * Authenticate with Salesforce using OAuth 2.0 Username-Password flow
   * @returns {Promise<Object>} Authentication response
   */
  async authenticate() {
    try {
      const authUrl = `${SALESFORCE_CONFIG.INSTANCE_URL}/services/oauth2/token`;

      const params = new window.URLSearchParams({
        grant_type: 'password',
        client_id: SALESFORCE_CONFIG.CLIENT_ID,
        client_secret: SALESFORCE_CONFIG.CLIENT_SECRET,
        username: SALESFORCE_CONFIG.USERNAME,
        password: SALESFORCE_CONFIG.PASSWORD + SALESFORCE_CONFIG.SECURITY_TOKEN,
      });

      const response = await window.fetch(authUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString(),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Salesforce auth failed: ${response.status} - ${errorData}`);
      }

      const authData = await response.json();

      this.accessToken = authData.access_token;
      this.instanceUrl = authData.instance_url;
      this.tokenExpiry = Date.now() + 3600 * 1000; // Token expires in 1 hour

      return {
        success: true,
        accessToken: this.accessToken,
        instanceUrl: this.instanceUrl,
        tokenType: authData.token_type,
        signature: authData.signature,
        issued_at: authData.issued_at,
      };
    } catch (error) {
      console.error('Salesforce authentication error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Check if current token is valid
   * @returns {boolean} True if token is valid
   */
  isTokenValid() {
    return this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry;
  }

  /**
   * Get valid access token (refresh if needed)
   * @returns {Promise<string|null>} Access token or null if failed
   */
  async getValidToken() {
    if (this.isTokenValid()) {
      return this.accessToken;
    }

    const authResult = await this.authenticate();
    return authResult.success ? this.accessToken : null;
  }

  /**
   * Get authorization headers for API calls
   * @returns {Promise<Object>} Headers object
   */
  async getAuthHeaders() {
    const token = await this.getValidToken();
    if (!token) {
      throw new Error('Failed to get valid Salesforce token');
    }

    return {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };
  }
}

/**
 * Salesforce API Client
 */
class SalesforceAPI {
  constructor() {
    this.auth = new SalesforceAuth();
    this.baseUrl = null;
  }

  /**
   * Initialize the API client
   * @returns {Promise<boolean>} True if initialized successfully
   */
  async init() {
    try {
      const authResult = await this.auth.authenticate();
      if (authResult.success) {
        this.baseUrl = `${this.auth.instanceUrl}/services/data/v${SALESFORCE_CONFIG.API_VERSION}`;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Salesforce API init error:', error);
      return false;
    }
  }

  /**
   * Make authenticated API request
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async request(endpoint, options = {}) {
    try {
      const headers = await this.auth.getAuthHeaders();

      const response = await window.fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
        timeout: SALESFORCE_CONFIG.TIMEOUT,
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Salesforce API error: ${response.status} - ${errorData}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Salesforce API request error:', error);
      throw error;
    }
  }

  /**
   * Create a new record
   * @param {string} objectType - Salesforce object type
   * @param {Object} data - Record data
   * @returns {Promise<Object>} Created record response
   */
  async createRecord(objectType, data) {
    return await this.request(`/sobjects/${objectType}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * Update an existing record
   * @param {string} objectType - Salesforce object type
   * @param {string} recordId - Record ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Update response
   */
  async updateRecord(objectType, recordId, data) {
    return await this.request(`/sobjects/${objectType}/${recordId}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  /**
   * Query records using SOQL
   * @param {string} query - SOQL query
   * @returns {Promise<Object>} Query results
   */
  async query(query) {
    const encodedQuery = encodeURIComponent(query);
    return await this.request(`/query?q=${encodedQuery}`);
  }

  /**
   * Get record by ID
   * @param {string} objectType - Salesforce object type
   * @param {string} recordId - Record ID
   * @param {string[]} fields - Fields to retrieve
   * @returns {Promise<Object>} Record data
   */
  async getRecord(objectType, recordId, fields = []) {
    const fieldsParam = fields.length > 0 ? `?fields=${fields.join(',')}` : '';
    return await this.request(`/sobjects/${objectType}/${recordId}${fieldsParam}`);
  }
}

/**
 * Validate Salesforce configuration
 * @returns {Object} Validation result
 */
export function validateSalesforceConfig() {
  const missing = [];

  if (!SALESFORCE_CONFIG.INSTANCE_URL || SALESFORCE_CONFIG.INSTANCE_URL.includes('your-org')) {
    missing.push('SALESFORCE_INSTANCE_URL');
  }
  if (!SALESFORCE_CONFIG.CLIENT_ID || SALESFORCE_CONFIG.CLIENT_ID.includes('your_')) {
    missing.push('SALESFORCE_CLIENT_ID');
  }
  if (!SALESFORCE_CONFIG.CLIENT_SECRET || SALESFORCE_CONFIG.CLIENT_SECRET.includes('your_')) {
    missing.push('SALESFORCE_CLIENT_SECRET');
  }
  if (!SALESFORCE_CONFIG.USERNAME || SALESFORCE_CONFIG.USERNAME.includes('your_')) {
    missing.push('SALESFORCE_USERNAME');
  }
  if (!SALESFORCE_CONFIG.PASSWORD || SALESFORCE_CONFIG.PASSWORD.includes('your_')) {
    missing.push('SALESFORCE_PASSWORD');
  }
  if (!SALESFORCE_CONFIG.SECURITY_TOKEN || SALESFORCE_CONFIG.SECURITY_TOKEN.includes('your_')) {
    missing.push('SALESFORCE_SECURITY_TOKEN');
  }

  return {
    isValid: missing.length === 0,
    missing,
    message:
      missing.length > 0
        ? `Configuração incompleta. Variáveis faltando: ${missing.join(', ')}`
        : 'Configuração válida',
  };
}

// Create singleton instances
export const salesforceAuth = new SalesforceAuth();
export const salesforceAPI = new SalesforceAPI();
export { SALESFORCE_CONFIG };
