# 🔄 Salesforce Integration Setup Guide

Este guia completo irá te ajudar a configurar a integração entre a calculadora Reino e o Salesforce.

## 📋 Pré-requisitos

- ✅ Calculadora funcionando com Supabase
- ✅ Acesso administrativo ao Salesforce
- ✅ Permissões para criar Connected Apps no Salesforce

## 🏗️ Parte 1: Configuração no Salesforce

### 1.1 Criar Connected App

1. **Acesse Setup no Salesforce:**
   - Clique no ícone de engrenagem (⚙️) no canto superior direito
   - Selecione "Setup"

2. **Navegue para App Manager:**
   - No menu lateral, vá em: `Apps` → `App Manager`
   - Clique em "New Connected App"

3. **Configure Basic Information:**

   ```
   Connected App Name: Reino Calculator Integration
   API Name: Reino_Calculator_Integration
   Contact Email: <EMAIL>
   Description: Integração automática da calculadora com Salesforce
   ```

4. **Configure OAuth Settings:**
   - ☑️ Enable OAuth Settings
   - **Callback URL:** `https://seudominio.com/oauth/callback`
   - **Selected OAuth Scopes:**
     - `Full access (full)`
     - `Perform requests on your behalf at any time (refresh_token, offline_access)`
     - `Access and manage your data (api)`

5. **Salve e Obtenha as Credenciais:**
   - Após salvar, anote o **Consumer Key** e **Consumer Secret**
   - Você precisará desses valores na configuração

### 1.2 Criar Custom Object

1. **Navegue para Object Manager:**
   - No Setup, vá em `Objects and Fields` → `Object Manager`
   - Clique em "Create" → "Custom Object"

2. **Configure o Custom Object:**

   ```
   Label: Calculator Submission
   Plural Label: Calculator Submissions
   Object Name: Calculator_Submission
   Record Name: Submission Number
   Data Type: Auto Number
   Display Format: CS-{00000}
   Starting Number: 1
   ```

3. **Criar Custom Fields:**

   Execute cada campo individualmente no Object Manager:

   **Campos de Identificação:**

   ```
   Field Label: Supabase ID
   Field Name: Supabase_ID
   Data Type: Text(255)
   Required: Yes
   Unique: Yes
   ```

   **Campos de Patrimônio:**

   ```
   Field Label: Patrimônio
   Field Name: Patrimonio
   Data Type: Currency(16,2)
   ```

   ```
   Field Label: Total Alocado
   Field Name: Total_Alocado
   Data Type: Currency(16,2)
   ```

   ```
   Field Label: Percentual Alocado
   Field Name: Percentual_Alocado
   Data Type: Percent(5,2)
   ```

   ```
   Field Label: Patrimônio Restante
   Field Name: Patrimonio_Restante
   Data Type: Currency(16,2)
   ```

   **Campos de Dados:**

   ```
   Field Label: Ativos Escolhidos
   Field Name: Ativos_Escolhidos
   Data Type: Long Text Area(32,768)
   ```

   ```
   Field Label: Alocação
   Field Name: Alocacao
   Data Type: Long Text Area(32,768)
   ```

   **Campos de Tracking:**

   ```
   Field Label: Data Submissão
   Field Name: Data_Submissao
   Data Type: Date/Time
   ```

   ```
   Field Label: User Agent
   Field Name: User_Agent
   Data Type: Text(255)
   ```

   ```
   Field Label: Session ID
   Field Name: Session_ID
   Data Type: Text(100)
   ```

   **Campos de Status:**

   ```
   Field Label: Status
   Field Name: Status
   Data Type: Picklist
   Values: Novo, Em Análise, Processado, Arquivado
   Default: Novo
   ```

   ```
   Field Label: Source
   Field Name: Source
   Data Type: Text(100)
   Default Value: Website Calculator
   ```

   **Campos Computados (opcionais):**

   ```
   Field Label: Categorias Selecionadas
   Field Name: Categorias_Selecionadas
   Data Type: Text(255)
   ```

   ```
   Field Label: Total Ativos Selecionados
   Field Name: Total_Ativos_Selecionados
   Data Type: Number(18,0)
   ```

### 1.3 Configurar Permissões

1. **Permission Sets (Recomendado):**
   - Vá em `Users` → `Permission Sets`
   - Crie um Permission Set: "Reino Calculator API Access"
   - Adicione permissões para o objeto `Calculator_Submission__c`:
     - Read, Create, Edit, Delete

2. **Ou Profile Permissions:**
   - Vá em `Users` → `Profiles`
   - Edite o profile do usuário de integração
   - Adicione permissões para o objeto custom

## 🔧 Parte 2: Configuração no Projeto

### 2.1 Aplicar Migration no Supabase

Execute o SQL no Supabase SQL Editor:

```sql
-- (Conteúdo do arquivo salesforce-sync-migration.sql)
```

### 2.2 Configurar Environment Variables

1. **Copie o arquivo de exemplo:**

   ```bash
   cp .env.salesforce.example .env.salesforce
   ```

2. **Configure as variáveis:**

   ```env
   SALESFORCE_CLIENT_ID=3MVG9...seu_consumer_key
   SALESFORCE_CLIENT_SECRET=seu_consumer_secret
   SALESFORCE_USERNAME=<EMAIL>
   SALESFORCE_PASSWORD=sua_senha
   SALESFORCE_SECURITY_TOKEN=seu_security_token
   SALESFORCE_INSTANCE_URL=https://seudominio.my.salesforce.com
   ```

### 2.3 Obter Security Token

1. **No Salesforce:**
   - Clique no seu avatar → "Settings"
   - Vá em "Reset My Security Token"
   - Clique em "Reset Security Token"
   - Verifique seu email para receber o token

2. **Combine Password + Security Token:**

   ```
   Se sua senha é: MinhaSenh@123
   E seu token é: AbCdEfGhIjKlMnOp
   
   Use no SALESFORCE_PASSWORD: MinhaSenh@123AbCdEfGhIjKlMnOp
   ```

## 🧪 Parte 3: Teste da Integração

### 3.1 Teste Local

1. **Ative o Debug Mode:**
   - Adicione `?debug=true` na URL da calculadora
   - Ou rode em localhost

2. **Verifique o Console:**

   ```javascript
   // No console do navegador, você verá:
   ✅ Salesforce Sync System initialized
   🔧 Salesforce debug tools available at window.salesforceSync
   
   // Para testar manualmente:
   window.salesforceSync.getStatus()
   window.salesforceSync.getSyncStatus()
   ```

3. **Teste uma Submissão:**
   - Complete a calculadora normalmente
   - Verifique o console para logs de sincronização
   - Confira no Salesforce se o registro foi criado

### 3.2 Dashboard de Debug

Com debug mode ativo, você verá um painel flutuante no canto superior direito com:

- Status da inicialização
- Tamanho da fila de sincronização
- Estatísticas de sync
- Botões para ações manuais

### 3.3 Verificação no Salesforce

1. **Vá para o App Launcher** (9 pontos)
2. **Procure por "Calculator Submissions"**
3. **Verifique se os registros estão sendo criados automaticamente**

## 🔍 Parte 4: Debugging e Troubleshooting

### 4.1 Problemas Comuns

**❌ Authentication Failed:**

```
Solução:
1. Verifique Consumer Key/Secret
2. Confirme Username/Password + Security Token
3. Teste as credenciais no Workbench do Salesforce
```

**❌ Object Not Found:**

```
Solução:
1. Confirme que o Custom Object foi criado
2. Verifique a API Name: Calculator_Submission__c
3. Confirme as permissões do usuário
```

**❌ Field Not Found:**

```
Solução:
1. Verifique se todos os custom fields foram criados
2. Confirme as API Names dos campos
3. Ajuste o mapeamento em salesforce-sync.js se necessário
```

### 4.2 Ferramentas de Debug

**Console Commands:**

```javascript
// Status geral
window.salesforceSync.getStatus()

// Status detalhado de sync
window.salesforceSync.getSyncStatus()

// Retentar syncs falha
window.salesforceSync.retryFailed()

// Sync manual de um registro específico
window.salesforceSync.manualSync('record-id-here')
```

**Supabase Queries:**

```sql
-- Ver status de sync
SELECT sync_status, COUNT(*) FROM calculator_submissions GROUP BY sync_status;

-- Ver registros com erro
SELECT id, sync_error, last_sync_attempt 
FROM calculator_submissions 
WHERE sync_status = 'failed';

-- Resetar registros falhados
UPDATE calculator_submissions 
SET sync_status = 'pending', sync_error = NULL 
WHERE sync_status = 'failed';
```

### 4.3 Logs e Monitoramento

**Browser Console:**

- Logs prefixados com emojis para fácil identificação
- ✅ Sucesso, ❌ Erro, 🔄 Processando, ⚠️ Aviso

**Supabase Table:**

- Campo `sync_status`: pending, synced, failed, manual
- Campo `sync_error`: Mensagem de erro detalhada
- Campo `last_sync_attempt`: Timestamp da última tentativa

## 🚀 Parte 5: Deploy em Produção

### 5.1 Environment Variables

Configure as variáveis de ambiente no seu provedor de hosting:

**Vercel:**

```bash
vercel env add SALESFORCE_CLIENT_ID
vercel env add SALESFORCE_CLIENT_SECRET
# ... etc
```

**Netlify:**

```bash
netlify env:set SALESFORCE_CLIENT_ID value
netlify env:set SALESFORCE_CLIENT_SECRET value
# ... etc
```

### 5.2 Disable Debug Mode

```env
SALESFORCE_DEBUG_MODE=false
```

### 5.3 Configure Monitoring

1. **Setup Salesforce Reports** para monitorar submissões
2. **Configure alertas** para falhas de sync
3. **Monitore o dashboard** do Supabase para erros

## 📊 Parte 6: Análise de Dados

### 6.1 Reports no Salesforce

Crie reports personalizados para analisar:

- Volume de submissões por período
- Patrimônio médio dos usuários
- Categorias de ativos mais populares
- Taxa de preenchimento da alocação

### 6.2 Dashboards

Configure dashboards para visualizar:

- Funil de conversão da calculadora
- Distribuição geográfica (se implementado)
- Tendências temporais
- Performance da integração

## 🔒 Parte 7: Segurança

### 7.1 Best Practices

- ✅ Use Permission Sets específicos
- ✅ Rotacione Security Tokens regularmente
- ✅ Monitor access logs no Salesforce
- ✅ Use profiles dedicados para integração
- ✅ Implemente rate limiting se necessário

### 7.2 Monitoramento

- Configure alertas para tentativas de login suspeitas
- Monitore API usage no Salesforce
- Implemente logging detalhado para auditoria

---

## 🎯 Resumo Final

Após seguir este guia, você terá:

✅ **Salesforce configurado** com Connected App e Custom Object
✅ **Supabase preparado** com campos de sincronização
✅ **Integração automática** funcionando em tempo real
✅ **Dashboard de debug** para monitoramento
✅ **Ferramentas de troubleshooting** configuradas

A integração irá automaticamente sincronizar todas as submissões da calculadora para o Salesforce, permitindo análise avançada e follow-up com leads.

Para suporte adicional, verifique os logs no console do navegador e use as ferramentas de debug fornecidas.
