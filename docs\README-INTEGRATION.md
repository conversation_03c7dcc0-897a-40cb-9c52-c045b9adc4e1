# 🎯 DGM Canvas - Teste de Integração com Reino Calculator

## 🚀 Como Configurar e Testar

### **1. Inicie o DGM Canvas**

```bash
cd "C:\Users\<USER>\Desktop\Zero\dgm-canvas"
npm run dev
```

O servidor estará rodando em: **<http://localhost:5173>**

### **2. Teste a Integração**

Abra no navegador: **`C:\Users\<USER>\Desktop\Zero\dgm-canvas\test-integration.html`**

### **3. O que você verá:**

#### **No DGM Canvas (localhost:5173):**

- ✅ Status de conexão no canto superior direito
- 📊 Canvas atualizado automaticamente quando dados chegarem
- 🎨 Visualização gráfica do patrimônio e alocação

#### **Na página de teste:**

- 🔍 Health check automático
- 📤 Botões para enviar dados de teste
- 📋 Verificação dos últimos dados recebidos

### **4. Como testar com o site real:**

1. **Abra o app-calc-reino** no navegador
2. **Preencha o formulário** completo
3. **Clique em "Enviar"**
4. **Veja os dados aparecerem** automaticamente no DGM Canvas

### **5. Endpoints Disponíveis:**

- **`POST /api/data`** - Recebe dados do app-calc-reino
- **`GET /api/health`** - Verifica se o servidor está ativo
- **`GET /api/latest`** - Retorna os últimos dados recebidos

### **6. Logs no Console:**

No terminal do DGM Canvas você verá:

```
📊 [DGM Canvas] Dados recebidos do app-calc-reino:
🏦 Patrimônio: { total: 100000, alocado: 85000, ... }
📈 Ativos: { escolhidos: [...], alocacao: [...] }
👤 Usuário: { nome: "João", email: "..." }
📋 Metadata: { source: "app-calc-reino", ... }
```

### **7. Solução de Problemas:**

#### **CORS Error:**

- ✅ **Já configurado** no vite.config.js

#### **Dados não aparecem:**

- Verifique se o DGM Canvas está rodando na porta 5173
- Confirme que o app-calc-reino está enviando para <http://localhost:5173/api/data>

#### **Canvas não atualiza:**

- Os dados são verificados a cada 2 segundos automaticamente
- Verifique o console do navegador para erros

### **8. Estrutura dos Dados Recebidos:**

```json
{
  "patrimonio": {
    "total": 250000,
    "alocado": 225000,
    "restante": 25000,
    "percentualAlocado": 90.00
  },
  "ativos": {
    "escolhidos": [...],
    "alocacao": [...],
    "resumo": {...}
  },
  "usuario": {
    "nome": "João Silva",
    "email": "<EMAIL>"
  },
  "metadata": {
    "source": "app-calc-reino",
    "hasTypebot": true,
    ...
  }
}
```

---

## 🎊 **Tudo Pronto!**

1. **Inicie**: `npm run dev` no dgm-canvas
2. **Teste**: Abra `test-integration.html`
3. **Use**: Preencha o formulário no app-calc-reino
4. **Veja**: Dados aparecem automaticamente no canvas!

**🔗 Links Rápidos:**

- DGM Canvas: <http://localhost:5173>
- Teste: `test-integration.html`
- Health: <http://localhost:5173/api/health>
