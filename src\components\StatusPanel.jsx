/**
 * StatusPanel Component
 * Displays connection status and data information
 */

import { useState, useEffect } from 'react';

export const StatusPanel = ({ dataManager, receivedData, lastUpdate }) => {
  const [connectionStatus, setConnectionStatus] = useState('Aguardando...');

  useEffect(() => {
    if (!dataManager) return;

    const handleDataReceived = () => {
      setConnectionStatus('✅ Dados recebidos!');
    };

    const handleError = () => {
      setConnectionStatus('❌ Erro de conexão');
    };

    dataManager.addEventListener('dataReceived', handleDataReceived);
    dataManager.addEventListener('error', handleError);

    return () => {
      dataManager.removeEventListener('dataReceived', handleDataReceived);
      dataManager.removeEventListener('error', handleError);
    };
  }, [dataManager]);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <div style={{
      position: 'absolute',
      top: 10,
      right: 10,
      zIndex: 1000,
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      minWidth: '200px'
    }}>
      <div><strong>🔗 Status:</strong> {connectionStatus}</div>
      {lastUpdate && (
        <div><strong>⏰ Última atualização:</strong><br/>{new Date(lastUpdate).toLocaleString('pt-BR')}</div>
      )}
      {receivedData && (
        <div style={{ marginTop: '10px' }}>
          <div><strong>👤 Usuário:</strong> {receivedData.usuario.nome || 'N/A'}</div>
          <div><strong>💰 Patrimônio:</strong> {formatCurrency(receivedData.patrimonio.total)}</div>
          <div><strong>📊 Alocado:</strong> {receivedData.patrimonio.percentualAlocado}%</div>
          <div><strong>🎯 Ativos:</strong> {receivedData.ativos.alocacao.length} tipos</div>
        </div>
      )}
    </div>
  );
};
