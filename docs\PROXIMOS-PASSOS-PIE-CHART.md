# 🎯 Próximos Passos - Gráfico de Torta Automático

## 📋 Status Atual

✅ **CONCLUÍDO - Sistema Modular Implementado**

- Arquitetura modular funcionando (DataManager, PieChart, StatusPanel, etc.)
- Transmissão de dados entre app-calc-reino → dgm-canvas funcionando
- Validação de dados implementada
- Polling automático de dados funcionando
- Componentes React modulares criados

✅ **CONCLUÍDO - Infraestrutura de Dados**

- Servidor Vite com endpoints `/api/data`, `/api/latest`, `/api/debug-log`
- DataManager recebendo e validando dados corretamente
- Sistema de logs para debug implementado
- Estrutura de dados normalizada

⚠️ **PENDENTE - Criação Automática do Gráfico**

- Dados estão sendo recebidos mas o gráfico não está sendo criado automaticamente
- PieChart.js existe e está funcional mas não está sendo acionado pelos dados

---

## 🎯 PRÓXIMO PASSO: Ativar Criação Automática do Gráfico

### 🔍 Problema Identificado

O sistema está **recebendo dados corretamente** mas o gráfico não está sendo **criado automaticamente**. Os logs mostram:

```
📊 Has data: true
📋 Data structure check: ✅ Todos os campos presentes
🐛 [Frontend Debug] [DataManager-INFO] Raw result received
```

Mas não há logs do PieChart sendo criado.

### 🎯 Solução Necessária

**Conectar o evento `dataReceived` do DataManager com a criação do PieChart.**

#### 📂 Arquivos a Verificar/Modificar

1. **`src/utils/useDGMCanvas.js`** - Hook que gerencia o DGM Canvas
2. **`src/App.jsx`** - Componente principal que orquestra tudo
3. **`src/charts/PieChart.js`** - Classe do gráfico (já implementada)

#### 🔧 Implementação Necessária

### 1. **Verificar o Hook useDGMCanvas.js**

```javascript
// src/utils/useDGMCanvas.js
const handleDataReceived = ({ data }) => {
  console.log("🎨 [useDGMCanvas] Data received in hook:", data);
  
  // ⚠️ VERIFICAR: Esta função está sendo chamada?
  updateCanvasWithData(data);
};

const updateCanvasWithData = (data) => {
  if (!editorRef.current || !data || !pieChartRef.current) {
    console.log("❌ Missing dependencies:", {
      hasEditor: !!editorRef.current,
      hasData: !!data,
      hasPieChart: !!pieChartRef.current
    });
    return;
  }

  // ⚠️ VERIFICAR: Esta função está criando o gráfico?
  pieChartRef.current.create(data.ativos.alocacao, data.patrimonio.total);
};
```

### 2. **Verificar se o Event Listener está Conectado**

```javascript
// src/utils/useDGMCanvas.js - useEffect
useEffect(() => {
  if (!dataManager) return;

  const handleDataReceived = ({ data }) => {
    console.log("🎨 [useDGMCanvas] Event received!"); // ⚠️ ADICIONAR LOG
    updateCanvasWithData(data);
  };

  // ⚠️ VERIFICAR: O evento está sendo registrado?
  dataManager.addEventListener("dataReceived", handleDataReceived);

  return () => {
    dataManager.removeEventListener("dataReceived", handleDataReceived);
  };
}, [dataManager]);
```

### 3. **Verificar se o DataManager está Disparando o Evento**

```javascript
// src/utils/DataManager.js - método notifyListeners
notifyListeners(event, data) {
  console.log(`🔔 [DataManager] Notifying ${event} listeners:`, data); // ⚠️ ADICIONAR LOG
  
  if (!this.listeners[event]) return;

  this.listeners[event].forEach((callback) => {
    try {
      callback(data);
    } catch (error) {
      console.error("❌ [DataManager] Error in event listener:", error);
    }
  });
}
```

---

## 🚀 Ações Imediatas

### **PASSO 1: Adicionar Logs de Debug**

Adicionar logs em pontos críticos para identificar onde o fluxo está quebrando:

1. **DataManager.js** - linha ~310 (método `notifyListeners`)
2. **useDGMCanvas.js** - linha ~102 (handleDataReceived)
3. **useDGMCanvas.js** - linha ~37 (updateCanvasWithData)
4. **PieChart.js** - linha ~38 (método create)

### **PASSO 2: Verificar Dependências**

Verificar se todas as dependências estão presentes quando o evento é disparado:

```javascript
console.log("🔍 Dependencies check:", {
  hasEditor: !!editorRef.current,
  hasData: !!data,
  hasPieChart: !!pieChartRef.current,
  hasDataManager: !!dataManager,
  dataManagerListeners: dataManager?.listeners
});
```

### **PASSO 3: Testar Criação Manual**

Adicionar botão para testar criação manual do gráfico:

```javascript
// Em App.jsx - temporário para teste
const testPieChart = () => {
  if (pieChartRef.current && dataManagerRef.current?.currentData) {
    const data = dataManagerRef.current.currentData;
    pieChartRef.current.create(data.ativos.alocacao, data.patrimonio.total);
  }
};

// Adicionar botão temporário
<button onClick={testPieChart}>🧪 Criar Gráfico Manualmente</button>
```

---

## 🎯 Resultado Esperado

Após implementar essas correções, quando dados forem recebidos do app-calc-reino, você deve ver:

```
🔔 [DataManager] Notifying dataReceived listeners: {...}
🎨 [useDGMCanvas] Event received!
🔍 Dependencies check: { hasEditor: true, hasData: true, ... }
🥧 [PieChart] Creating chart with: {...}
🥧 [PieChart] Chart created with 4 slices
```

E o **gráfico de torta interativo** deve aparecer automaticamente no DGM Canvas!

---

## 🔧 Comandos para Testar

```bash
# Terminal 1 - DGM Canvas
cd dgm-canvas
npm run dev

# Terminal 2 - App Calc Reino  
cd app-calc-reino
npm run dev

# Navegar para http://localhost:5173
# Preencher formulário no app-calc-reino
# Verificar se gráfico aparece automaticamente no DGM Canvas
```

---

## 📊 Dados de Teste

Caso precise testar com dados fixos, adicione em `useDGMCanvas.js`:

```javascript
// Dados de exemplo para teste
const testData = {
  patrimonio: {
    total: 9000,
    alocado: 9000
  },
  ativos: {
    alocacao: [
      { product: 'Poupança', value: 3200, valueFormatted: 'R$ 3.200,00', percentageFormatted: '36%' },
      { product: 'Criptoativos', value: 3000, valueFormatted: 'R$ 3.000,00', percentageFormatted: '33%' },
      { product: 'Previdência', value: 1800, valueFormatted: 'R$ 1.800,00', percentageFormatted: '20%' },
      { product: 'COE', value: 1000, valueFormatted: 'R$ 1.000,00', percentageFormatted: '11%' }
    ]
  },
  usuario: { nome: 'João' }
};
```

---

## 🎯 Meta Final

**Quando concluído, o usuário deve:**

1. Preencher o formulário no app-calc-reino
2. Ver o gráfico de torta aparecer **automaticamente** no DGM Canvas
3. Poder **interagir** com o gráfico (mover, selecionar elementos)
4. Ver a **legenda** e **informações** atualizadas em tempo real

**🎉 Resultado: Gráfico de torta interativo e movível criado automaticamente quando dados forem recebidos!**
