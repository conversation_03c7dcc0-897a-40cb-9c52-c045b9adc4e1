/**
 * Development Server Setup for History API
 * Creates endpoints for DGM Canvas to access historical data
 */

import { setupHistoryAPI } from './api/history-server-v2.js';

/**
 * Initialize history API for development
 * This should be called when running in development mode
 */
export function initHistoryAPI() {
  // Check if we're in a Node.js environment (server-side)
  if (typeof window === 'undefined' && typeof process !== 'undefined') {
    console.log('🚀 [HistoryInit] Initializing History API for server environment...');

    // This would be used with Express or other Node.js servers
    // For now, we'll just log that it's available
    // eslint-disable-next-line no-console
    console.log('✅ [HistoryInit] History API ready for integration');

    return {
      setupHistoryAPI,
      message: 'Use setupHistoryAPI(server) to configure endpoints',
    };
  }

  // Client-side: provide helper functions for DGM Canvas integration
  // eslint-disable-next-line no-console
  console.log('🌐 [HistoryInit] History API client helpers loaded');

  return {
    // Helper function to get the base URL for history API
    getHistoryBaseURL() {
      const isDevelopment =
        window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        window.location.search.includes('debug=true');

      if (isDevelopment) {
        // In development, assume history API runs on port 3001
        return 'http://localhost:3001/api/history';
      }

      // In production, same domain
      return `${window.location.origin}/api/history`;
    },

    // Helper to test history API connection
    async testHistoryConnection() {
      try {
        const baseURL = this.getHistoryBaseURL();
        const response = await fetch(baseURL);

        if (response.ok) {
          const data = await response.json();
          // eslint-disable-next-line no-console
          console.log('✅ [HistoryAPI] Connection test successful:', data.version);
          return { success: true, data };
        }

        console.warn('⚠️ [HistoryAPI] Connection test failed:', response.status);
        return { success: false, status: response.status };
      } catch (error) {
        console.warn('⚠️ [HistoryAPI] Connection test error:', error.message);
        return { success: false, error: error.message };
      }
    },

    // Helper to fetch recent submissions for DGM Canvas
    async fetchRecentSubmissions(limit = 10) {
      try {
        const baseURL = this.getHistoryBaseURL();
        const response = await fetch(`${baseURL}/recent?limit=${limit}`);

        if (response.ok) {
          return await response.json();
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        console.error('❌ [HistoryAPI] Error fetching recent submissions:', error);
        return { success: false, error: error.message };
      }
    },

    // Helper to fetch statistics
    async fetchStats() {
      try {
        const baseURL = this.getHistoryBaseURL();
        const response = await fetch(`${baseURL}/stats`);

        if (response.ok) {
          return await response.json();
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        console.error('❌ [HistoryAPI] Error fetching stats:', error);
        return { success: false, error: error.message };
      }
    },

    // Helper to search submissions
    async searchSubmissions(searchTerm, limit = 20) {
      try {
        const baseURL = this.getHistoryBaseURL();
        const response = await fetch(
          `${baseURL}/search?q=${encodeURIComponent(searchTerm)}&limit=${limit}`
        );

        if (response.ok) {
          return await response.json();
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        console.error('❌ [HistoryAPI] Error searching submissions:', error);
        return { success: false, error: error.message };
      }
    },
  };
}

// Initialize immediately for global access
export const historyAPI = initHistoryAPI();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.ReinoHistoryAPI = historyAPI;
}
