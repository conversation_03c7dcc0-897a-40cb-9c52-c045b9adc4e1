-- Adicionar colunas de nome e e-mail à tabela existente
-- Execute este script no Supabase SQL Editor

-- ⚠️ ERRO ENCONTRADO: A coluna 'typebot_result_id' não existe na tabela
-- ✅ SOLUÇÃO: Execute este script completo para adicionar todas as colunas necessárias

-- 🔧 CORREÇÃO PARA ERRO DE VIEW: 
-- Se der erro "cannot change name of view column", executar o script completo
-- que primeiro remove a view existente e depois recria com as novas colunas

-- Adiciona as novas colunas
ALTER TABLE calculator_submissions 
ADD COLUMN IF NOT EXISTS nome TEXT,
ADD COLUMN IF NOT EXISTS email TEXT,
ADD COLUMN IF NOT EXISTS typebot_session_id TEXT,
ADD COLUMN IF NOT EXISTS typebot_result_id TEXT;

-- Adici<PERSON> comentários às colunas para documentação
COMMENT ON COLUMN calculator_submissions.nome IS 'Nome do usuário coletado via Typebot';
COMMENT ON COLUMN calculator_submissions.email IS 'E-mail do usuário coletado via Typebot';
COMMENT ON COLUMN calculator_submissions.typebot_session_id IS 'ID da sessão do Typebot';
COMMENT ON COLUMN calculator_submissions.typebot_result_id IS 'ID do resultado do Typebot';

-- Adiciona índices para facilitar buscas
CREATE INDEX IF NOT EXISTS idx_calculator_submissions_email 
ON calculator_submissions(email);

CREATE INDEX IF NOT EXISTS idx_calculator_submissions_nome 
ON calculator_submissions(nome);

CREATE INDEX IF NOT EXISTS idx_calculator_submissions_typebot_session 
ON calculator_submissions(typebot_session_id);

CREATE INDEX IF NOT EXISTS idx_calculator_submissions_typebot_result 
ON calculator_submissions(typebot_result_id);

-- Remove a view existente se houver conflito
DROP VIEW IF EXISTS v_calculator_with_contact;

-- View para relatórios com informações de contato
CREATE VIEW v_calculator_with_contact AS
SELECT 
  id,
  nome,
  email,
  patrimonio,
  total_alocado,
  percentual_alocado,
  patrimonio_restante,
  submitted_at,
  ativos_escolhidos,
  alocacao,
  user_agent,
  session_id,
  typebot_session_id,
  typebot_result_id,
  created_at,
  updated_at
FROM calculator_submissions
WHERE nome IS NOT NULL AND email IS NOT NULL
ORDER BY submitted_at DESC;

-- Política RLS para a view (se RLS estiver habilitado)
-- ALTER VIEW v_calculator_with_contact OWNER TO authenticated;
