# 🎯 DGM Canvas - Gráfico de Torta Interativo

Este projeto implementa um gráfico de torta interativo e movível usando DGM.js para visualizar dados do Reino Calculator.

## 🚀 Funcionalidades Implementadas

### ✅ Gráfico de Torta Interativo

- **Fatias coloridas**: Cada ativo tem uma cor diferente
- **Labels percentuais**: Valores percentuais dentro das fatias
- **Legenda lateral**: Lista completa com cores e valores
- **Totalmente interativo**: Todas as partes são selecionáveis e movíveis

### ✅ Visualização de Dados

- **Patrimônio total**: Exibição do valor total e alocado
- **Distribuição por ativos**: Visualização clara de cada tipo de investimento
- **Informações do usuário**: Nome e dados pessoais
- **Timestamp**: Horário da última atualização

### ✅ Integração Completa

- **Recebimento automático**: Dados enviados do app-calc-reino
- **Atualização em tempo real**: Canvas atualiza quando novos dados chegam
- **Status de conexão**: Feedback visual do status da integração

## 🎨 Como Usar

### 1. Iniciar o DGM Canvas

```bash
cd dgm-canvas
npm run dev
```

### 2. Iniciar o Reino Calculator

```bash
cd app-calc-reino
npm run dev
```

### 3. Testar o Gráfico

Para testar com dados exemplo, acesse:

```
http://localhost:5173?test=true
```

## 🔧 Estrutura do Código

### Função Principal: `createInteractivePieChart()`

```javascript
const createInteractivePieChart = (editor, alocacao, total) => {
  // Parâmetros do gráfico
  const centerX = 400;
  const centerY = 350;
  const radius = 120;
  
  // Cores predefinidas para as fatias
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', ...];
  
  // Criação das fatias usando DGM.js
  // Cada fatia é um polígono interativo
}
```

### Características das Fatias

- **Forma**: Polígonos fechados criados com `editor.factory.createLine()`
- **Interatividade**: `movable: true` e `selectable: true`
- **Cores**: Paleta de cores diferenciadas
- **Labels**: Textos com percentuais posicionados no centro das fatias

### Legenda Lateral

- **Quadrados coloridos**: Indicadores visuais das cores
- **Textos informativos**: Nome do produto e valor formatado
- **Totalmente movível**: Cada elemento pode ser reposicionado

## 📊 Estrutura dos Dados

O gráfico espera dados no formato:

```javascript
{
  patrimonio: {
    total: 100000,
    alocado: 75000,
    percentualAlocado: 75
  },
  ativos: {
    alocacao: [
      {
        product: 'Renda Fixa',
        value: 30000,
        valueFormatted: 'R$ 30.000',
        percentageFormatted: '30%'
      }
      // ... mais ativos
    ]
  },
  usuario: {
    nome: 'Nome do Usuário'
  }
}
```

## 🎯 Recursos Interativos

### Seleção e Movimento

- **Clique**: Seleciona qualquer elemento do gráfico
- **Arrastar**: Move elementos selecionados
- **Multi-seleção**: Selecione múltiplos elementos

### Elementos Movíveis

- ✅ Fatias da torta
- ✅ Labels percentuais
- ✅ Legenda (quadrados e textos)
- ✅ Título do gráfico
- ✅ Borda do gráfico

## 🎨 Personalização

### Cores

Modifique o array `colors` para personalizar as cores das fatias:

```javascript
const colors = [
  '#FF6B6B', // Vermelho
  '#4ECDC4', // Turquesa
  '#45B7D1', // Azul
  // ... adicione mais cores
];
```

### Posicionamento

Ajuste as coordenadas para reposicionar o gráfico:

```javascript
const centerX = 400; // Posição X do centro
const centerY = 350; // Posição Y do centro
const radius = 120;  // Raio do gráfico
```

## 🐛 Debug e Teste

### Logs de Debug

O sistema inclui logs detalhados:

- 📊 Dados recebidos do app-calc-reino
- 🎨 Status de inicialização do editor
- ✅ Confirmação de criação dos elementos

### Modo de Teste

Acesse com `?test=true` para carregar dados de exemplo e testar o gráfico sem depender do Reino Calculator.

## 🔄 Fluxo de Dados

1. **Reino Calculator** → Processa formulário
2. **Envio HTTP** → POST para `/api/data`
3. **DGM Canvas** → Recebe dados via polling
4. **Renderização** → Cria gráfico de torta interativo
5. **Interação** → Usuário pode mover e selecionar elementos

## 📈 Próximos Passos

### Possíveis Melhorias

- [ ] Animações de transição entre dados
- [ ] Tooltips com informações detalhadas
- [ ] Zoom e pan no gráfico
- [ ] Exportação da visualização
- [ ] Gráficos de linha temporal
- [ ] Comparação entre períodos

### Otimizações

- [ ] Cache de elementos gráficos
- [ ] Lazy loading de dados
- [ ] WebSocket para updates em tempo real
- [ ] Compressão de dados transmitidos

---

🎯 **Desenvolvido com DGM.js - Canvas interativo com formas inteligentes**
