import { useState, useEffect } from 'react';

/**
 * ChartControls Component  
 * Provides interactive controls to adjust chart data values
 * Uses D3.js engine for chart rendering
 */
export function ChartControls({ pie<PERSON>hart, d3PieChart, currentData, onDataChange, editor, isVisible = true }) {
  // Original data backup for reset functionality
  const [originalData, setOriginalData] = useState(null);
  const [editableData, setEditableData] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('values');
  
  // Engine fixo D3.js - sem opção de troca
  
  // Settings/configurations state - apenas configurações essenciais
  const [settings, setSettings] = useState({
    allowBudgetOverrun: false,
    allowPercentageOverrun: false,
    showGrid: true,
    // Drawing tools settings
    drawingMode: false
  });

  // Initialize data when currentData changes
  useEffect(() => {
    if (currentData && !originalData) {
      setOriginalData(JSON.parse(JSON.stringify(currentData))); // Deep copy
      setEditableData(JSON.parse(JSON.stringify(currentData))); // Deep copy
    } else if (currentData) {
      // Update editable data when new data comes in
      setEditableData(JSON.parse(JSON.stringify(currentData)));
    }
  }, [currentData, originalData]);

  // Apply initial grid setting when editor becomes available
  useEffect(() => {
    if (editor && settings.showGrid) {
      try {
        editor.setShowGrid(settings.showGrid);
      } catch (error) {
        console.error('❌ [ChartControls] Error applying initial grid to editor:', error);
      }
    }
  }, [editor, settings.showGrid]);

  // Configure drawing tool properties when settings change
  useEffect(() => {
    if (editor && settings.drawingMode) {
      try {
        // Activate the Freehand handler
        editor.activateHandler('Freehand');
        editor.setActiveHandlerLock(true);
        
        console.log('🎨 [ChartControls] Drawing mode activated: Freehand');
        
      } catch (error) {
        console.error('❌ [ChartControls] Error configuring drawing tool properties:', error);
      }
    }
  }, [editor, settings.drawingMode]);

  // Handle individual asset value changes
  const handleAssetValueChange = (index, newValue) => {
    if (!editableData) return;
    
    const updatedData = { ...editableData };
    let numericValue = parseFloat(newValue) || 0;
    
    // Calculate what the total would be with this change
    const currentAssetValue = updatedData.ativos.alocacao[index].value;
    const otherAssetsTotal = updatedData.ativos.alocacao.reduce((sum, item, i) => 
      i !== index ? sum + item.value : sum, 0
    );
    const potentialTotal = otherAssetsTotal + numericValue;
    const totalPatrimonio = updatedData.patrimonio.total;
    
    // Apply budget overrun restriction if setting is disabled
    if (!settings.allowBudgetOverrun && potentialTotal > totalPatrimonio) {
      const maxAllowed = Math.max(0, totalPatrimonio - otherAssetsTotal);
      numericValue = maxAllowed;
      console.warn('⚠️ [ChartControls] Budget overrun blocked. Value limited to:', maxAllowed);
    }
    
    // Apply percentage overrun restriction if setting is disabled
    if (!settings.allowPercentageOverrun) {
      const maxPercentageValue = totalPatrimonio; // 100% of patrimonio
      if (potentialTotal > maxPercentageValue) {
        const maxAllowed = Math.max(0, maxPercentageValue - otherAssetsTotal);
        numericValue = Math.min(numericValue, maxAllowed);
        console.warn('⚠️ [ChartControls] Percentage overrun blocked. Value limited to:', numericValue);
      }
    }
    
    // Update the asset value
    updatedData.ativos.alocacao[index].value = numericValue;
    
    // Recalculate totals
    const totalAllocated = updatedData.ativos.alocacao.reduce((sum, item) => sum + item.value, 0);
    const remaining = totalPatrimonio - totalAllocated;
    
    // Update remaining in the data structure
    updatedData.patrimonio.alocado = totalAllocated;
    updatedData.patrimonio.restante = remaining;
    
    // Update formatted values and percentages
    updatedData.ativos.alocacao[index].valueFormatted = formatCurrency(numericValue);
    updatedData.ativos.alocacao[index].percentageFormatted = `${calculatePercentage(numericValue, totalPatrimonio)}%`;
    
    setEditableData(updatedData);
    
    // Notify parent component of the change
    if (onDataChange) {
      onDataChange(updatedData);
    }
    
    console.log('💰 [ChartControls] Updated asset value:', {
      index,
      requestedValue: parseFloat(newValue) || 0,
      appliedValue: numericValue,
      totalAllocated,
      remaining,
      budgetAllowed: settings.allowBudgetOverrun,
      percentageAllowed: settings.allowPercentageOverrun
    });
  };

  // Handle total patrimony change
  const handleTotalPatrimonyChange = (newTotal) => {
    if (!editableData) return;
    
    const updatedData = { ...editableData };
    const numericTotal = parseFloat(newTotal) || 0;
    
    updatedData.patrimonio.total = numericTotal;
    updatedData.patrimonio.totalFormatted = formatCurrency(numericTotal);
    
    // Recalculate remaining and percentages
    let totalAllocated = updatedData.ativos.alocacao.reduce((sum, item) => sum + item.value, 0);
    
    // If budget overrun is not allowed and current allocation exceeds new total, adjust proportionally
    if (!settings.allowBudgetOverrun && totalAllocated > numericTotal && numericTotal > 0) {
      const scaleFactor = numericTotal / totalAllocated;
      console.warn('⚠️ [ChartControls] Adjusting allocations proportionally due to budget restriction. Scale factor:', scaleFactor);
      
      updatedData.ativos.alocacao.forEach((asset, index) => {
        const adjustedValue = asset.value * scaleFactor;
        updatedData.ativos.alocacao[index].value = adjustedValue;
        updatedData.ativos.alocacao[index].valueFormatted = formatCurrency(adjustedValue);
        updatedData.ativos.alocacao[index].percentageFormatted = `${calculatePercentage(adjustedValue, numericTotal)}%`;
      });
      
      // Recalculate total after adjustments
      totalAllocated = updatedData.ativos.alocacao.reduce((sum, item) => sum + item.value, 0);
    }
    
    updatedData.patrimonio.alocado = totalAllocated;
    updatedData.patrimonio.restante = numericTotal - totalAllocated;
    updatedData.patrimonio.alocadoFormatted = formatCurrency(totalAllocated);
    updatedData.patrimonio.restanteFormatted = formatCurrency(numericTotal - totalAllocated);
    
    // Update percentages for all assets (if not already updated above)
    if (settings.allowBudgetOverrun || totalAllocated <= numericTotal) {
      updatedData.ativos.alocacao.forEach((asset) => {
        asset.percentageFormatted = `${calculatePercentage(asset.value, numericTotal)}%`;
      });
    }
    
    setEditableData(updatedData);
    
    if (onDataChange) {
      onDataChange(updatedData);
    }
    
    console.log('🏦 [ChartControls] Updated total patrimony:', {
      newTotal: numericTotal,
      totalAllocated,
      remaining: numericTotal - totalAllocated,
      budgetAllowed: settings.allowBudgetOverrun,
      wasAdjusted: !settings.allowBudgetOverrun && updatedData.ativos.alocacao.some(asset => asset.value !== updatedData.ativos.alocacao.find((a, i) => i === updatedData.ativos.alocacao.indexOf(asset))?.value)
    });
  };

  // Handle settings changes
  const handleSettingChange = (settingKey, value) => {
    const updatedSettings = {
      ...settings,
      [settingKey]: value
    };
    setSettings(updatedSettings);
    
    // Apply grid setting to DGM editor
    if (settingKey === 'showGrid' && editor) {
      try {
        editor.setShowGrid(value);
        console.log('🔧 [ChartControls] Grid applied to editor:', value);
      } catch (error) {
        console.error('❌ [ChartControls] Error applying grid to editor:', error);
      }
    }
    
    // Apply drawing mode settings to DGM editor
    if (settingKey === 'drawingMode' && editor) {
      try {
        if (value) {
          // Activate drawing handler
          editor.activateHandler('Freehand');
          editor.setActiveHandlerLock(true);
          console.log('🎨 [ChartControls] Drawing mode activated: Freehand');
        } else {
          // Deactivate drawing handler
          editor.activateDefaultHandler();
          editor.setActiveHandlerLock(false);
          console.log('🎨 [ChartControls] Drawing mode deactivated');
        }
      } catch (error) {
        console.error('❌ [ChartControls] Error applying drawing mode to editor:', error);
      }
    }
    
    console.log('⚙️ [ChartControls] Settings updated:', {
      setting: settingKey,
      value,
      allSettings: updatedSettings
    });
  };

  // Funções relacionadas à troca de engine removidas - mantendo apenas D3.js

  // Clear all drawings from canvas
  const clearAllDrawings = () => {
    if (editor) {
      try {
        const currentPage = editor.getCurrentPage();
        if (currentPage && currentPage.children.length > 0) {
          // Get all shapes except the pie chart (if any)
          const allShapes = currentPage.children.filter(shape => 
            // Keep only pie chart related shapes, remove freehand/highlighter drawings
            !['Freehand', 'Highlighter'].includes(shape.type)
          );
          
          // Remove all drawings (freehand shapes)
          const drawingShapes = currentPage.children.filter(shape => 
            shape.type === 'Freehand'
          );
          
          if (drawingShapes.length > 0) {
            editor.actions.remove(drawingShapes);
            console.log('🧹 [ChartControls] Cleared all drawings:', drawingShapes.length, 'shapes removed');
          } else {
            console.log('🧹 [ChartControls] No drawings to clear');
          }
        }
      } catch (error) {
        console.error('❌ [ChartControls] Error clearing drawings:', error);
      }
    }
  };

  // Reset to original values
  const resetToOriginal = () => {
    if (originalData) {
      const resetData = JSON.parse(JSON.stringify(originalData));
      setEditableData(resetData);
      
      if (onDataChange) {
        onDataChange(resetData);
      }
      
      console.log('🔄 [ChartControls] Reset to original data:', resetData);
    }
  };

  // Format currency for display
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0);
  };

  // Calculate percentage
  const calculatePercentage = (value, total) => {
    if (!total || total === 0) return 0;
    return ((value / total) * 100).toFixed(1);
  };

  if (!isVisible || !editableData) return null;

  return (
    <div style={styles.container}>
      {/* Toggle Button */}
      <button 
        onClick={() => setIsExpanded(!isExpanded)}
        style={styles.toggleButton}
        title="Controles dos Valores"
      >
        💰 {isExpanded ? 'Ocultar' : 'Editar Valores'}
      </button>

      {/* Controls Panel */}
      {isExpanded && (
        <div style={styles.panel}>
          {/* Header */}
          <div style={styles.header}>
            <h3 style={styles.title}>💰 Editar Valores do Gráfico</h3>
            <div style={styles.headerButtons}>
              <button onClick={resetToOriginal} style={styles.resetButton}>
                🔄 Valores Originais
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div style={styles.tabs}>
            <button 
              onClick={() => setActiveTab('values')}
              style={{...styles.tab, ...(activeTab === 'values' ? styles.activeTab : {})}}
            >
              💰 Alocações
            </button>
            <button 
              onClick={() => setActiveTab('totals')}
              style={{...styles.tab, ...(activeTab === 'totals' ? styles.activeTab : {})}}
            >
              🏦 Patrimônio
            </button>
            <button 
              onClick={() => setActiveTab('chart')}
              style={{...styles.tab, ...(activeTab === 'chart' ? styles.activeTab : {})}}
            >
              📊 Gráfico
            </button>
            <button 
              onClick={() => setActiveTab('settings')}
              style={{...styles.tab, ...(activeTab === 'settings' ? styles.activeTab : {})}}
            >
              ⚙️ Configurações
            </button>
          </div>

          {/* Tab Content */}
          <div style={styles.content}>
            {activeTab === 'values' && (
              <div style={styles.section}>
                <div style={styles.sectionHeader}>
                  <h4 style={styles.sectionTitle}>💼 Alocação por Produto</h4>
                </div>
                
                {editableData.ativos.alocacao.map((asset, index) => (
                  <div key={index} style={styles.assetControl}>
                    <div style={styles.assetHeader}>
                      <span style={styles.assetName}>
                        {asset.product || asset.nome}
                      </span>
                      <span style={styles.assetPercentage}>
                        {calculatePercentage(asset.value, editableData.patrimonio.total)}%
                      </span>
                    </div>
                    
                    <div style={styles.assetInputs}>
                      <input
                        type="number"
                        value={asset.value}
                        onChange={(e) => handleAssetValueChange(index, e.target.value)}
                        style={styles.valueInput}
                        step="1000"
                        min="0"
                      />
                      <span style={styles.currency}>
                        {formatCurrency(asset.value)}
                      </span>
                    </div>
                    
                    <input
                      type="range"
                      min="0"
                      max={settings.allowBudgetOverrun ? editableData.patrimonio.total * 2 : editableData.patrimonio.total}
                      value={asset.value}
                      onChange={(e) => handleAssetValueChange(index, e.target.value)}
                      style={styles.assetSlider}
                      title={settings.allowBudgetOverrun ? 
                        "Sem limite de orçamento" : 
                        `Máximo permitido: ${formatCurrency(editableData.patrimonio.total)}`
                      }
                    />
                  </div>
                ))}
                
                <div style={styles.totalsDisplay}>
                  <div style={styles.totalItem}>
                    <span>Total Alocado:</span>
                    <span style={styles.totalValue}>
                      {formatCurrency(editableData.patrimonio.alocado)}
                    </span>
                  </div>
                  <div style={styles.totalItem}>
                    <span>Restante:</span>
                    <span style={{...styles.totalValue, color: editableData.patrimonio.restante < 0 ? '#f44336' : '#4caf50'}}>
                      {formatCurrency(editableData.patrimonio.restante)}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'totals' && (
              <div style={styles.section}>
                <div style={styles.sectionHeader}>
                  <h4 style={styles.sectionTitle}>🏦 Patrimônio Total</h4>
                </div>
                
                <div style={styles.patrimonioControl}>
                  <label style={styles.patrimonioLabel}>Patrimônio Total:</label>
                  <div style={styles.patrimonioInputs}>
                    <input
                      type="number"
                      value={editableData.patrimonio.total}
                      onChange={(e) => handleTotalPatrimonyChange(e.target.value)}
                      style={styles.patrimonioInput}
                      step="1000"
                      min="0"
                    />
                    <span style={styles.currency}>
                      {formatCurrency(editableData.patrimonio.total)}
                    </span>
                  </div>
                </div>
                
                <div style={styles.patrimonioSummary}>
                  <div style={styles.summaryItem}>
                    <span style={styles.summaryLabel}>💼 Total Alocado:</span>
                    <span style={styles.summaryValue}>
                      {formatCurrency(editableData.patrimonio.alocado)}
                    </span>
                  </div>
                  
                  <div style={styles.summaryItem}>
                    <span style={styles.summaryLabel}>💰 Restante:</span>
                    <span style={{
                      ...styles.summaryValue,
                      color: editableData.patrimonio.restante < 0 ? '#f44336' : '#4caf50'
                    }}>
                      {formatCurrency(editableData.patrimonio.restante)}
                    </span>
                  </div>
                  
                  <div style={styles.summaryItem}>
                    <span style={styles.summaryLabel}>📊 % Alocado:</span>
                    <span style={styles.summaryValue}>
                      {calculatePercentage(editableData.patrimonio.alocado, editableData.patrimonio.total)}%
                    </span>
                  </div>
                </div>
                
                {editableData.patrimonio.restante < 0 && (
                  <div style={styles.warning}>
                    ⚠️ Atenção: Valor alocado excede o patrimônio total!
                  </div>
                )}
              </div>
            )}

            {activeTab === 'chart' && (
              <div style={styles.section}>
                <div style={styles.sectionHeader}>
                  <h4 style={styles.sectionTitle}>📊 Configurações do Gráfico</h4>
                </div>
                
                {/* Engine D3.js fixo - sem opção de troca */}
              </div>
            )}

            {activeTab === 'settings' && (
              <div style={styles.section}>
                <div style={styles.sectionHeader}>
                  <h4 style={styles.sectionTitle}>⚙️ Configurações do Sistema</h4>
                </div>
                
                {/* Sistema Settings */}
                <div style={styles.settingsControl}>
                  <div style={styles.settingItem}>
                    <div style={styles.settingInfo}>
                      <span style={styles.settingLabel}>🚫 Permitir ultrapassar orçamento</span>
                      <span style={styles.settingDescription}>
                        Permite que a alocação total exceda o patrimônio disponível
                      </span>
                    </div>
                    <label style={styles.switch}>
                      <input
                        type="checkbox"
                        checked={settings.allowBudgetOverrun}
                        onChange={(e) => handleSettingChange('allowBudgetOverrun', e.target.checked)}
                        style={styles.switchInput}
                      />
                      <span style={{
                        ...styles.slider,
                        backgroundColor: settings.allowBudgetOverrun ? '#4caf50' : '#ccc'
                      }}>
                        <span style={{
                          ...styles.sliderButton,
                          transform: settings.allowBudgetOverrun ? 'translateX(24px)' : 'translateX(0px)'
                        }}></span>
                      </span>
                    </label>
                  </div>

                  <div style={styles.settingItem}>
                    <div style={styles.settingInfo}>
                      <span style={styles.settingLabel}>📊 Permitir ultrapassar 100%</span>
                      <span style={styles.settingDescription}>
                        Permite que a soma das porcentagens exceda 100% do patrimônio
                      </span>
                    </div>
                    <label style={styles.switch}>
                      <input
                        type="checkbox"
                        checked={settings.allowPercentageOverrun}
                        onChange={(e) => handleSettingChange('allowPercentageOverrun', e.target.checked)}
                        style={styles.switchInput}
                      />
                      <span style={{
                        ...styles.slider,
                        backgroundColor: settings.allowPercentageOverrun ? '#4caf50' : '#ccc'
                      }}>
                        <span style={{
                          ...styles.sliderButton,
                          transform: settings.allowPercentageOverrun ? 'translateX(24px)' : 'translateX(0px)'
                        }}></span>
                      </span>
                    </label>
                  </div>

                  <div style={styles.settingItem}>
                    <div style={styles.settingInfo}>
                      <span style={styles.settingLabel}>📐 Mostrar grid de fundo</span>
                      <span style={styles.settingDescription}>
                        Exibe um grid visual de fundo para auxiliar no alinhamento e composição
                      </span>
                    </div>
                    <label style={styles.switch}>
                      <input
                        type="checkbox"
                        checked={settings.showGrid}
                        onChange={(e) => handleSettingChange('showGrid', e.target.checked)}
                        style={styles.switchInput}
                      />
                      <span style={{
                        ...styles.slider,
                        backgroundColor: settings.showGrid ? '#4caf50' : '#ccc'
                      }}>
                        <span style={{
                          ...styles.sliderButton,
                          transform: settings.showGrid ? 'translateX(24px)' : 'translateX(0px)'
                        }}></span>
                      </span>
                    </label>
                  </div>
                </div>

                {/* Drawing Tools Settings */}
                <div style={styles.sectionHeader}>
                  <h4 style={styles.sectionTitle}>🎨 Ferramentas de Desenho</h4>
                </div>
                
                <div style={styles.settingsControl}>
                  <div style={styles.settingItem}>
                    <div style={styles.settingInfo}>
                      <span style={styles.settingLabel}>✏️ Modo de desenho</span>
                      <span style={styles.settingDescription}>
                        Ativa as ferramentas de desenho livre no canvas (largura: 1.5px, cor padrão)
                      </span>
                    </div>
                    <label style={styles.switch}>
                      <input
                        type="checkbox"
                        checked={settings.drawingMode}
                        onChange={(e) => handleSettingChange('drawingMode', e.target.checked)}
                        style={styles.switchInput}
                      />
                      <span style={{
                        ...styles.slider,
                        backgroundColor: settings.drawingMode ? '#4caf50' : '#ccc'
                      }}>
                        <span style={{
                          ...styles.sliderButton,
                          transform: settings.drawingMode ? 'translateX(24px)' : 'translateX(0px)'
                        }}></span>
                      </span>
                    </label>
                  </div>

                  {settings.drawingMode && (
                    <>
                      <div style={styles.clearButtonContainer}>
                        <button
                          onClick={clearAllDrawings}
                          style={styles.clearButton}
                          title="Remove todos os desenhos do canvas"
                        >
                          🧹 Limpar Todos os Desenhos
                        </button>
                      </div>
                    </>
                  )}
                </div>

                <div style={styles.settingsInfo}>
                  <div style={styles.infoItem}>
                    <span style={styles.infoLabel}>�💡 Status atual:</span>
                  </div>
                  <div style={styles.infoItem}>
                    <span style={styles.infoDetail}>
                      • Orçamento: {settings.allowBudgetOverrun ? '✅ Sem limites' : '⛔ Limitado ao patrimônio'}
                    </span>
                  </div>
                  <div style={styles.infoItem}>
                    <span style={styles.infoDetail}>
                      • Porcentagem: {settings.allowPercentageOverrun ? '✅ Pode exceder 100%' : '⛔ Limitado a 100%'}
                    </span>
                  </div>
                  <div style={styles.infoItem}>
                    <span style={styles.infoDetail}>
                      • Grid de fundo: {settings.showGrid ? '✅ Ativado' : '⛔ Desativado'}
                    </span>
                  </div>
                  <div style={styles.infoItem}>
                    <span style={styles.infoDetail}>
                      • Modo desenho: {settings.drawingMode ? '✅ Pincel ativo' : '⛔ Desativado'}
                    </span>
                  </div>
                  <div style={styles.infoItem}>
                    <span style={styles.infoDetail}>
                      • Total alocado: {formatCurrency(editableData.patrimonio.alocado)} ({calculatePercentage(editableData.patrimonio.alocado, editableData.patrimonio.total)}%)
                    </span>
                  </div>
                  <div style={styles.infoItem}>
                    <span style={styles.infoDetail}>
                      • Patrimônio total: {formatCurrency(editableData.patrimonio.total)}
                    </span>
                  </div>
                  
                  {(!settings.allowBudgetOverrun && editableData.patrimonio.restante < 0) && (
                    <div style={styles.settingsWarning}>
                      ⚠️ Configuração ativa: Valores serão limitados ao patrimônio disponível ({formatCurrency(editableData.patrimonio.total)})
                    </div>
                  )}
                  
                  {(!settings.allowPercentageOverrun && calculatePercentage(editableData.patrimonio.alocado, editableData.patrimonio.total) > 100) && (
                    <div style={styles.settingsWarning}>
                      ⚠️ Configuração ativa: Alocação será limitada a 100% do patrimônio
                    </div>
                  )}

                  {(settings.allowBudgetOverrun && settings.allowPercentageOverrun) && (
                    <div style={styles.settingsSuccess}>
                      ✅ Modo livre: Sem limites de orçamento ou porcentagem ativados
                    </div>
                  )}

                  {settings.drawingMode && (
                    <div style={styles.drawingInfo}>
                      🎨 Desenho ativo: Pincel | 
                      Largura: 1.5px | 
                      Cor: Padrão do sistema
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Current Values Display */}
          <div style={styles.statusBar}>
            <small style={styles.statusText}>
              👤 {editableData.usuario.nome} | 
              💰 {formatCurrency(editableData.patrimonio.total)} | 
              📊 {editableData.ativos.alocacao.length} produtos
            </small>
          </div>
        </div>
      )}
    </div>
  );
}

// Styles
const styles = {
  container: {
    position: 'fixed',
    top: '60px',
    left: '10px',
    zIndex: 1000,
    fontFamily: 'Arial, sans-serif'
  },
  toggleButton: {
    padding: '8px 12px',
    backgroundColor: '#2196F3',
    color: 'white',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '12px',
    fontWeight: 'bold',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
    transition: 'all 0.3s ease'
  },
  panel: {
    marginTop: '8px',
    backgroundColor: 'white',
    border: '1px solid #ddd',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
    minWidth: '350px',
    maxWidth: '450px'
  },
  header: {
    padding: '12px',
    borderBottom: '1px solid #eee',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  title: {
    margin: 0,
    fontSize: '14px',
    color: '#333'
  },
  headerButtons: {
    display: 'flex',
    gap: '8px'
  },
  resetButton: {
    padding: '4px 8px',
    backgroundColor: '#FF9800',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '10px'
  },
  tabs: {
    display: 'flex',
    borderBottom: '1px solid #eee'
  },
  tab: {
    flex: 1,
    padding: '8px 12px',
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    fontSize: '11px',
    color: '#666',
    borderBottomWidth: '2px',
    borderBottomStyle: 'solid',
    borderBottomColor: 'transparent'
  },
  activeTab: {
    color: '#2196F3',
    borderBottomColor: '#2196F3',
    backgroundColor: '#f8f9fa'
  },
  content: {
    padding: '16px',
    maxHeight: '400px',
    overflowY: 'auto'
  },
  section: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px'
  },
  sectionHeader: {
    marginBottom: '8px'
  },
  sectionTitle: {
    margin: 0,
    fontSize: '12px',
    color: '#555',
    fontWeight: 'bold'
  },
  assetControl: {
    padding: '12px',
    backgroundColor: '#f8f9fa',
    borderRadius: '6px',
    border: '1px solid #e0e0e0'
  },
  assetHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '8px'
  },
  assetName: {
    fontSize: '11px',
    fontWeight: 'bold',
    color: '#333'
  },
  assetPercentage: {
    fontSize: '10px',
    color: '#666',
    backgroundColor: '#e3f2fd',
    padding: '2px 6px',
    borderRadius: '10px'
  },
  assetInputs: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    marginBottom: '8px'
  },
  valueInput: {
    padding: '4px 8px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '11px',
    width: '100px'
  },
  currency: {
    fontSize: '10px',
    color: '#666',
    fontStyle: 'italic'
  },
  assetSlider: {
    width: '100%',
    height: '6px',
    borderRadius: '3px',
    outline: 'none',
    cursor: 'pointer'
  },
  totalsDisplay: {
    marginTop: '16px',
    padding: '12px',
    backgroundColor: '#e8f5e8',
    borderRadius: '6px',
    border: '1px solid #c8e6c9'
  },
  totalItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    fontSize: '11px',
    marginBottom: '4px'
  },
  totalValue: {
    fontWeight: 'bold'
  },
  patrimonioControl: {
    padding: '12px',
    backgroundColor: '#f3e5f5',
    borderRadius: '6px',
    border: '1px solid #e1bee7'
  },
  patrimonioLabel: {
    display: 'block',
    fontSize: '12px',
    fontWeight: 'bold',
    color: '#333',
    marginBottom: '8px'
  },
  patrimonioInputs: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  patrimonioInput: {
    padding: '6px 12px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '12px',
    width: '120px'
  },
  patrimonioSummary: {
    marginTop: '16px',
    padding: '12px',
    backgroundColor: '#fff3e0',
    borderRadius: '6px',
    border: '1px solid #ffcc02'
  },
  summaryItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    fontSize: '11px',
    marginBottom: '6px'
  },
  summaryLabel: {
    color: '#666'
  },
  summaryValue: {
    fontWeight: 'bold'
  },
  warning: {
    marginTop: '12px',
    padding: '8px',
    backgroundColor: '#ffebee',
    border: '1px solid #f44336',
    borderRadius: '4px',
    fontSize: '11px',
    color: '#c62828',
    textAlign: 'center'
  },
  statusBar: {
    padding: '8px 12px',
    backgroundColor: '#f8f9fa',
    borderTop: '1px solid #eee',
    borderRadius: '0 0 8px 8px'
  },
  statusText: {
    color: '#666',
    fontSize: '10px'
  },
  // Settings styles
  settingsControl: {
    padding: '16px',
    backgroundColor: '#f8f9fa',
    borderRadius: '6px',
    border: '1px solid #e9ecef'
  },
  settingItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '16px',
    paddingBottom: '16px',
    borderBottom: '1px solid #e9ecef'
  },
  settingInfo: {
    flex: 1,
    marginRight: '16px'
  },
  settingLabel: {
    display: 'block',
    fontSize: '12px',
    fontWeight: 'bold',
    color: '#333',
    marginBottom: '4px'
  },
  settingDescription: {
    fontSize: '10px',
    color: '#666',
    lineHeight: '1.4'
  },
  switch: {
    position: 'relative',
    display: 'inline-block',
    width: '48px',
    height: '24px'
  },
  switchInput: {
    opacity: 0,
    width: 0,
    height: 0
  },
  slider: {
    position: 'absolute',
    cursor: 'pointer',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    transition: '0.3s',
    borderRadius: '24px',
    display: 'flex',
    alignItems: 'center',
    padding: '2px'
  },
  sliderButton: {
    height: '18px',
    width: '18px',
    backgroundColor: 'white',
    transition: '0.3s',
    borderRadius: '50%',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
  },
  settingsInfo: {
    marginTop: '16px',
    padding: '12px',
    backgroundColor: '#e8f5e8',
    borderRadius: '6px',
    border: '1px solid #c8e6c9'
  },
  infoItem: {
    marginBottom: '6px'
  },
  infoLabel: {
    fontSize: '11px',
    fontWeight: 'bold',
    color: '#333'
  },
  infoDetail: {
    fontSize: '10px',
    color: '#666',
    marginLeft: '8px'
  },
  settingsWarning: {
    marginTop: '8px',
    padding: '8px',
    backgroundColor: '#fff3cd',
    border: '1px solid #ffeaa7',
    borderRadius: '4px',
    fontSize: '10px',
    color: '#856404'
  },
  settingsSuccess: {
    marginTop: '8px',
    padding: '8px',
    backgroundColor: '#d4edda',
    border: '1px solid #c3e6cb',
    borderRadius: '4px',
    fontSize: '10px',
    color: '#155724'
  },
  // Drawing tools styles
  colorPickerContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  colorPicker: {
    width: '40px',
    height: '25px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    cursor: 'pointer'
  },
  colorValue: {
    fontSize: '10px',
    color: '#666',
    fontFamily: 'monospace',
    backgroundColor: '#f5f5f5',
    padding: '2px 6px',
    borderRadius: '3px'
  },
  sliderContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    minWidth: '120px'
  },
  rangeSlider: {
    flex: 1,
    height: '4px',
    borderRadius: '2px',
    outline: 'none',
    cursor: 'pointer'
  },
  sliderValue: {
    fontSize: '10px',
    color: '#666',
    minWidth: '35px',
    textAlign: 'right',
    fontWeight: 'bold'
  },
  clearButtonContainer: {
    marginTop: '16px',
    padding: '12px',
    backgroundColor: '#fff3cd',
    border: '1px solid #ffeaa7',
    borderRadius: '6px',
    textAlign: 'center'
  },
  clearButton: {
    padding: '8px 16px',
    backgroundColor: '#ff6b35',
    color: 'white',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '11px',
    fontWeight: 'bold',
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  },
  drawingInfo: {
    marginTop: '8px',
    padding: '8px',
    backgroundColor: '#e3f2fd',
    border: '1px solid #bbdefb',
    borderRadius: '4px',
    fontSize: '10px',
    color: '#1565c0',
    fontFamily: 'monospace'
  },
  // D3.js Chart Controls Styles
  select: {
    padding: '6px 10px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '11px',
    backgroundColor: 'white',
    cursor: 'pointer',
    minWidth: '150px'
  },
  button: {
    padding: '6px 12px',
    border: 'none',
    borderRadius: '4px',
    fontSize: '10px',
    fontWeight: 'bold',
    color: 'white',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  },
  inputSlider: {
    flex: 1,
    height: '4px',
    borderRadius: '2px',
    outline: 'none',
    cursor: 'pointer',
    background: 'linear-gradient(to right, #ddd 0%, #4caf50 100%)'
  }
};
