#!/usr/bin/env node

/**
 * 🚀 Deploy Script for DGM Canvas History Integration
 *
 * This script automates the deployment process for production
 */

import { execSync } from "child_process";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  dgmCanvasPath: path.resolve(__dirname, ".."),
  appCalcReinoPath: path.resolve(
    __dirname,
    "../../../Integração Reino/app-calc-reino"
  ),
  buildOutputPath: path.resolve(__dirname, "../dist"),
  backupPath: path.resolve(__dirname, "../backups"),

  // Deployment targets
  production: {
    dgmCanvasUrl: "https://dgm-canvas.seu-dominio.com",
    apiUrl: "https://api.seu-dominio.com",
    historyApiPort: 3001,
  },
};

// Utility functions
const log = (message, type = "info") => {
  const timestamp = new Date().toISOString();
  const prefix =
    {
      info: "🔍",
      success: "✅",
      error: "❌",
      warning: "⚠️",
      deploy: "🚀",
    }[type] || "📝";

  console.log(`${prefix} [${timestamp}] ${message}`);
};

const exec = (command, options = {}) => {
  log(`Executing: ${command}`, "info");
  try {
    const result = execSync(command, {
      stdio: "inherit",
      cwd: options.cwd || process.cwd(),
      ...options,
    });
    return result;
  } catch (error) {
    log(`Command failed: ${command}`, "error");
    throw error;
  }
};

const checkFileExists = (filePath) => {
  if (!fs.existsSync(filePath)) {
    log(`File not found: ${filePath}`, "error");
    return false;
  }
  return true;
};

// Deployment steps
const deploymentSteps = {
  async checkPrerequisites() {
    log("Checking prerequisites...", "deploy");

    // Check if required paths exist
    const requiredPaths = [
      CONFIG.dgmCanvasPath,
      CONFIG.appCalcReinoPath,
      path.join(CONFIG.dgmCanvasPath, "package.json"),
      path.join(CONFIG.appCalcReinoPath, "package.json"),
    ];

    for (const filePath of requiredPaths) {
      if (!checkFileExists(filePath)) {
        throw new Error(`Missing required file: ${filePath}`);
      }
    }

    // Check Node.js version
    const nodeVersion = process.version;
    log(`Node.js version: ${nodeVersion}`, "info");

    if (parseInt(nodeVersion.slice(1)) < 16) {
      throw new Error("Node.js 16+ is required");
    }

    log("Prerequisites check passed", "success");
  },

  async backupExistingFiles() {
    log("Creating backup...", "deploy");

    if (!fs.existsSync(CONFIG.backupPath)) {
      fs.mkdirSync(CONFIG.backupPath, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const backupDir = path.join(CONFIG.backupPath, `backup-${timestamp}`);

    fs.mkdirSync(backupDir, { recursive: true });

    // Backup current dist if exists
    if (fs.existsSync(CONFIG.buildOutputPath)) {
      exec(
        `cp -r "${CONFIG.buildOutputPath}" "${path.join(backupDir, "dist")}"`,
        {
          cwd: CONFIG.dgmCanvasPath,
        }
      );
    }

    log(`Backup created: ${backupDir}`, "success");
    return backupDir;
  },

  async installDependencies() {
    log("Installing dependencies...", "deploy");

    // Install DGM Canvas dependencies
    log("Installing DGM Canvas dependencies...", "info");
    exec("npm ci", { cwd: CONFIG.dgmCanvasPath });

    // Install app-calc-reino dependencies
    log("Installing app-calc-reino dependencies...", "info");
    exec("npm install express cors winston express-rate-limit", {
      cwd: CONFIG.appCalcReinoPath,
    });

    log("Dependencies installed", "success");
  },

  async runTests() {
    log("Running tests...", "deploy");

    try {
      // Run DGM Canvas tests if they exist
      if (fs.existsSync(path.join(CONFIG.dgmCanvasPath, "package.json"))) {
        const packageJson = JSON.parse(
          fs.readFileSync(
            path.join(CONFIG.dgmCanvasPath, "package.json"),
            "utf8"
          )
        );

        if (packageJson.scripts && packageJson.scripts.test) {
          exec("npm test", { cwd: CONFIG.dgmCanvasPath });
        }
      }

      log("Tests passed", "success");
    } catch (error) {
      log("Tests failed - continuing with warnings", "warning");
      // Don't fail deployment on test failures in this example
    }
  },

  async buildProduction() {
    log("Building for production...", "deploy");

    // Set production environment
    process.env.NODE_ENV = "production";

    // Build DGM Canvas
    log("Building DGM Canvas...", "info");
    exec("npm run build", {
      cwd: CONFIG.dgmCanvasPath,
      env: { ...process.env, NODE_ENV: "production" },
    });

    // Verify build output
    if (!fs.existsSync(CONFIG.buildOutputPath)) {
      throw new Error("Build failed - no dist folder found");
    }

    const buildFiles = fs.readdirSync(CONFIG.buildOutputPath);
    log(`Build completed. Files: ${buildFiles.join(", ")}`, "success");
  },

  async setupProductionServer() {
    log("Setting up production server files...", "deploy");

    // Create production server file if it doesn't exist
    const prodServerPath = path.join(
      CONFIG.appCalcReinoPath,
      "src",
      "history-server-prod.js"
    );

    if (!fs.existsSync(prodServerPath)) {
      const serverTemplate = `/**
 * Production History API Server
 * Auto-generated by deploy script
 */

import express from 'express';
import cors from 'cors';
import { setupHistoryAPI } from './api/history-server-v2.js';

const app = express();
const PORT = process.env.HISTORY_API_PORT || 3001;

// Middleware
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ${JSON.stringify(CONFIG.production.dgmCanvasUrl)}
    : ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'history-api',
    version: '1.0.0'
  });
});

// Setup history API routes
setupHistoryAPI(app);

// Error handling
app.use((err, req, res, next) => {
  console.error('❌ [HistoryAPI] Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(\`🚀 History API Server running on port \${PORT}\`);
  console.log(\`🌍 Environment: \${process.env.NODE_ENV || 'development'}\`);
});

export default app;`;

      fs.writeFileSync(prodServerPath, serverTemplate);
      log("Production server file created", "success");
    }

    // Create PM2 ecosystem file
    const ecosystemPath = path.join(
      CONFIG.appCalcReinoPath,
      "ecosystem.config.js"
    );

    if (!fs.existsSync(ecosystemPath)) {
      const ecosystemConfig = `module.exports = {
  apps: [
    {
      name: 'history-api',
      script: 'src/history-server-prod.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      env: {
        NODE_ENV: 'production',
        HISTORY_API_PORT: ${CONFIG.production.historyApiPort}
      }
    }
  ]
};`;

      fs.writeFileSync(ecosystemPath, ecosystemConfig);
      log("PM2 ecosystem file created", "success");
    }
  },

  async validateDeployment() {
    log("Validating deployment...", "deploy");

    // Check if build files exist
    const requiredBuildFiles = ["index.html"];

    for (const file of requiredBuildFiles) {
      const filePath = path.join(CONFIG.buildOutputPath, file);
      if (!checkFileExists(filePath)) {
        throw new Error(`Missing required build file: ${file}`);
      }
    }

    // Check if production server files exist
    const prodServerPath = path.join(
      CONFIG.appCalcReinoPath,
      "src",
      "history-server-prod.js"
    );
    if (!checkFileExists(prodServerPath)) {
      throw new Error("Production server file not found");
    }

    log("Deployment validation passed", "success");
  },

  async generateDeploymentReport() {
    log("Generating deployment report...", "deploy");

    const report = {
      timestamp: new Date().toISOString(),
      environment: "production",
      dgmCanvas: {
        buildPath: CONFIG.buildOutputPath,
        buildFiles: fs.readdirSync(CONFIG.buildOutputPath),
        size: this.getDirectorySize(CONFIG.buildOutputPath),
      },
      backend: {
        serverFile: path.join(
          CONFIG.appCalcReinoPath,
          "src",
          "history-server-prod.js"
        ),
        ecosystemFile: path.join(
          CONFIG.appCalcReinoPath,
          "ecosystem.config.js"
        ),
      },
      nextSteps: [
        "1. Copy dist/ folder to web server",
        "2. Start PM2: pm2 start ecosystem.config.js",
        "3. Configure nginx proxy",
        "4. Test endpoints: curl http://localhost:3001/health",
        "5. Monitor logs: pm2 logs history-api",
      ],
    };

    const reportPath = path.join(
      CONFIG.dgmCanvasPath,
      "deployment-report.json"
    );
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    log(`Deployment report saved: ${reportPath}`, "success");
    return report;
  },

  getDirectorySize(dirPath) {
    let totalSize = 0;

    const walk = (dir) => {
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        if (stats.isDirectory()) {
          walk(filePath);
        } else {
          totalSize += stats.size;
        }
      }
    };

    walk(dirPath);
    return `${(totalSize / 1024 / 1024).toFixed(2)} MB`;
  },
};

// Main deployment function
async function deploy() {
  log("🚀 Starting deployment process...", "deploy");

  try {
    await deploymentSteps.checkPrerequisites();
    await deploymentSteps.backupExistingFiles();
    await deploymentSteps.installDependencies();
    await deploymentSteps.runTests();
    await deploymentSteps.buildProduction();
    await deploymentSteps.setupProductionServer();
    await deploymentSteps.validateDeployment();
    const report = await deploymentSteps.generateDeploymentReport();

    log("🎉 Deployment completed successfully!", "success");
    log("📋 Next steps:", "info");

    report.nextSteps.forEach((step, index) => {
      log(`   ${step}`, "info");
    });

    log("📊 Build information:", "info");
    log(`   📁 Build size: ${report.dgmCanvas.size}`, "info");
    log(`   📄 Files: ${report.dgmCanvas.buildFiles.length}`, "info");
  } catch (error) {
    log(`Deployment failed: ${error.message}`, "error");
    log("🔧 Please fix the error and try again", "warning");
    process.exit(1);
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];

  switch (command) {
    case "deploy":
      deploy();
      break;

    case "check":
      deploymentSteps.checkPrerequisites();
      break;

    case "build":
      deploymentSteps.buildProduction();
      break;

    default:
      console.log(`
🚀 DGM Canvas Deployment Script

Usage:
  node deploy.js deploy   - Full deployment process
  node deploy.js check    - Check prerequisites only
  node deploy.js build    - Build for production only

Environment:
  NODE_ENV=production     - Set production environment
      `);
  }
}
