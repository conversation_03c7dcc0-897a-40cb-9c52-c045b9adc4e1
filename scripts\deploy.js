#!/usr/bin/env node

/**
 * 🚀 Deploy Script Node.js - App Calc Reino & DGM Canvas
 * Versão: 1.0
 * Última atualização: Janeiro 2025
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Cores para console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Funções de logging
const log = (message) =>
  console.log(`${colors.blue}[${new Date().toISOString()}]${colors.reset} ${message}`);
const success = (message) => console.log(`${colors.green}✅ ${message}${colors.reset}`);
const warning = (message) => console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
const error = (message) => {
  console.error(`${colors.red}❌ ${message}${colors.reset}`);
  process.exit(1);
};

// Verificar se estamos no diretório correto
const checkWorkspace = () => {
  const appCalcReino = path.join(process.cwd(), 'app-calc-reino');
  const dgmCanvas = path.join(process.cwd(), 'dgm-canvas');

  if (!fs.existsSync(appCalcReino) || !fs.existsSync(dgmCanvas)) {
    error(
      'Execute este script na raiz do workspace (onde estão as pastas app-calc-reino e dgm-canvas)'
    );
  }
};

// Verificar dependências
const checkDependencies = () => {
  log('Verificando dependências...');

  try {
    execSync('node --version', { stdio: 'ignore' });
  } catch {
    error('Node.js não encontrado. Instale Node.js 18+ primeiro.');
  }

  try {
    execSync('pnpm --version', { stdio: 'ignore' });
  } catch {
    warning('pnpm não encontrado. Instalando...');
    try {
      execSync('npm install -g pnpm', { stdio: 'inherit' });
    } catch {
      error('Falha ao instalar pnpm');
    }
  }

  try {
    execSync('git --version', { stdio: 'ignore' });
  } catch {
    error('Git não encontrado. Instale Git primeiro.');
  }

  success('Dependências verificadas');
};

// Carregar variáveis de ambiente
const loadEnvVars = () => {
  const envPath = path.join(process.cwd(), '.env');
  if (fs.existsSync(envPath)) {
    log('Carregando variáveis de ambiente...');
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach((line) => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
    success('Variáveis de ambiente carregadas');
  }
};

// Verificar variáveis de ambiente obrigatórias
const checkEnvVars = () => {
  log('Verificando variáveis de ambiente...');

  const requiredVars = ['SUPABASE_URL', 'SUPABASE_ANON_KEY', 'SUPABASE_SERVICE_ROLE_KEY'];

  const missingVars = requiredVars.filter((varName) => !process.env[varName]);

  if (missingVars.length > 0) {
    error(`Variáveis de ambiente não definidas: ${missingVars.join(', ')}`);
  }

  success('Variáveis de ambiente verificadas');
};

// Build App-Calc-Reino
const buildAppCalcReino = () => {
  log('Iniciando build do App-Calc-Reino...');

  const appDir = path.join(process.cwd(), 'app-calc-reino');

  try {
    // Instalar dependências
    log('Instalando dependências...');
    execSync('pnpm install --frozen-lockfile', {
      cwd: appDir,
      stdio: 'inherit',
    });

    // Build de produção
    log('Criando build de produção...');
    execSync('pnpm run build', {
      cwd: appDir,
      stdio: 'inherit',
    });

    // Verificar se build foi criado
    const distPath = path.join(appDir, 'dist');
    if (!fs.existsSync(distPath)) {
      error('Build não foi criado. Verifique o comando de build.');
    }

    success('App-Calc-Reino pronto para deploy');
  } catch (err) {
    error(`Falha no build do App-Calc-Reino: ${err.message}`);
  }
};

// Build DGM Canvas
const buildDgmCanvas = () => {
  log('Iniciando build do DGM Canvas...');

  const canvasDir = path.join(process.cwd(), 'dgm-canvas');

  try {
    // Instalar dependências
    log('Instalando dependências...');
    execSync('npm ci', {
      cwd: canvasDir,
      stdio: 'inherit',
    });

    // Build de produção
    log('Criando build de produção...');
    execSync('npm run build', {
      cwd: canvasDir,
      stdio: 'inherit',
    });

    // Verificar se build foi criado
    const distPath = path.join(canvasDir, 'dist');
    if (!fs.existsSync(distPath)) {
      error('Build não foi criado. Verifique o comando de build.');
    }

    success('DGM Canvas pronto para deploy');
  } catch (err) {
    error(`Falha no build do DGM Canvas: ${err.message}`);
  }
};

// Deploy para Vercel
const deployToVercel = () => {
  log('Fazendo deploy para Vercel...');

  try {
    // Verificar se Vercel CLI está instalado
    try {
      execSync('vercel --version', { stdio: 'ignore' });
    } catch {
      log('Instalando Vercel CLI...');
      execSync('npm install -g vercel', { stdio: 'inherit' });
    }

    const canvasDir = path.join(process.cwd(), 'dgm-canvas');

    // Deploy para produção
    execSync('vercel --prod --confirm', {
      cwd: canvasDir,
      stdio: 'inherit',
    });

    success('Deploy para Vercel concluído');
  } catch (err) {
    error(`Falha no deploy para Vercel: ${err.message}`);
  }
};

// Mostrar instruções para Render
const showRenderInstructions = () => {
  console.log('\n📋 Instruções para deploy no Render:\n');
  console.log('1. Acesse https://render.com e conecte seu repositório');
  console.log('2. Configure as seguintes variáveis de ambiente:');
  console.log('   - NODE_ENV=production');
  console.log(`   - SUPABASE_URL=${process.env.SUPABASE_URL || 'your_supabase_url'}`);
  console.log(
    `   - SUPABASE_ANON_KEY=${process.env.SUPABASE_ANON_KEY || 'your_supabase_anon_key'}`
  );
  console.log(`   - SUPABASE_SERVICE_ROLE_KEY=your_service_role_key`);
  console.log('3. Build Command: pnpm install && pnpm run build');
  console.log('4. Start Command: pnpm run serve');
  console.log('');
  success('Instruções para Render exibidas');
};

// Criar arquivos de configuração
const createConfigFiles = () => {
  log('Criando arquivos de configuração...');

  // Dockerfile para app-calc-reino
  const dockerfilePath = path.join(process.cwd(), 'app-calc-reino', 'Dockerfile');
  if (!fs.existsSync(dockerfilePath)) {
    const dockerfileContent = `FROM node:18-alpine

WORKDIR /app

# Instalar pnpm
RUN npm install -g pnpm

# Copiar arquivos de dependências
COPY package.json pnpm-lock.yaml ./

# Instalar dependências
RUN pnpm install --frozen-lockfile

# Copiar código fonte
COPY . .

# Build da aplicação
RUN pnpm run build

# Expor porta
EXPOSE 3000

# Comando de inicialização
CMD ["pnpm", "run", "serve"]
`;
    fs.writeFileSync(dockerfilePath, dockerfileContent);
    success('Dockerfile criado para app-calc-reino');
  }

  // vercel.json para dgm-canvas
  const vercelJsonPath = path.join(process.cwd(), 'dgm-canvas', 'vercel.json');
  if (!fs.existsSync(vercelJsonPath)) {
    const vercelConfig = {
      framework: 'vite',
      buildCommand: 'npm run build',
      outputDirectory: 'dist',
      devCommand: 'npm run dev',
      installCommand: 'npm install',
      functions: {
        'api/**/*.js': {
          runtime: 'nodejs18.x',
        },
      },
      rewrites: [
        {
          source: '/api/:path*',
          destination: '/api/:path*',
        },
      ],
    };
    fs.writeFileSync(vercelJsonPath, JSON.stringify(vercelConfig, null, 2));
    success('vercel.json criado para dgm-canvas');
  }

  // netlify.toml para dgm-canvas
  const netlifyTomlPath = path.join(process.cwd(), 'dgm-canvas', 'netlify.toml');
  if (!fs.existsSync(netlifyTomlPath)) {
    const netlifyConfig = `[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
`;
    fs.writeFileSync(netlifyTomlPath, netlifyConfig);
    success('netlify.toml criado para dgm-canvas');
  }

  // .env.example
  const envExamplePath = path.join(process.cwd(), '.env.example');
  if (!fs.existsSync(envExamplePath)) {
    const envExample = `# App-Calc-Reino Configuration
NODE_ENV=production
PORT=3000

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# DGM Canvas
VITE_API_BASE_URL=https://your-app-calc-reino-domain.com
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
`;
    fs.writeFileSync(envExamplePath, envExample);
    success('.env.example criado');
  }
};

// Testar conexão com Supabase
const testSupabaseConnection = async () => {
  log('Testando conexão com Supabase...');

  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
    warning('Credenciais do Supabase não configuradas, pulando teste de conexão');
    return;
  }

  try {
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(`${process.env.SUPABASE_URL}/rest/v1/`, {
      headers: {
        apikey: process.env.SUPABASE_ANON_KEY,
        Authorization: `Bearer ${process.env.SUPABASE_ANON_KEY}`,
      },
    });

    if (response.ok) {
      success('Conexão com Supabase OK');
    } else {
      warning(`Falha na conexão com Supabase (HTTP ${response.status})`);
    }
  } catch (err) {
    warning(`Erro ao testar conexão com Supabase: ${err.message}`);
  }
};

// Função principal
const main = async () => {
  console.log('\n🚀 Deploy Automático - App Calc Reino & DGM Canvas');
  console.log('==================================================\n');

  const command = process.argv[2];

  if (!command) {
    console.log('Uso: node deploy.js [comando]');
    console.log('\nComandos disponíveis:');
    console.log('  check   - Verificar dependências e configurações');
    console.log('  build   - Build local de ambos os projetos');
    console.log('  vercel  - Deploy DGM Canvas para Vercel');
    console.log('  render  - Instruções para deploy no Render');
    console.log('  config  - Criar arquivos de configuração');
    return;
  }

  checkWorkspace();
  loadEnvVars();

  try {
    switch (command) {
      case 'check':
        checkDependencies();
        checkEnvVars();
        await testSupabaseConnection();
        success('Verificações concluídas');
        break;

      case 'build':
        checkDependencies();
        buildAppCalcReino();
        buildDgmCanvas();
        success('Build completo concluído');
        break;

      case 'vercel':
        checkDependencies();
        buildDgmCanvas();
        deployToVercel();
        break;

      case 'render':
        checkDependencies();
        buildAppCalcReino();
        showRenderInstructions();
        break;

      case 'config':
        createConfigFiles();
        success('Arquivos de configuração criados');
        break;

      default:
        error(`Comando desconhecido: ${command}`);
    }

    console.log('\n🎉 Script concluído!');
    console.log('\n📋 Próximos passos:');
    console.log('- Verifique os logs de deploy das plataformas');
    console.log('- Configure DNS se necessário');
    console.log('- Teste as aplicações em produção');
    console.log('- Configure monitoramento\n');
  } catch (err) {
    error(`Erro durante execução: ${err.message}`);
  }
};

// Executar função principal
if (require.main === module) {
  main().catch((err) => {
    error(`Erro fatal: ${err.message}`);
  });
}

module.exports = {
  checkWorkspace,
  checkDependencies,
  buildAppCalcReino,
  buildDgmCanvas,
  deployToVercel,
  createConfigFiles,
};
