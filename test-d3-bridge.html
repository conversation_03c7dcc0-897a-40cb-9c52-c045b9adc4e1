<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Teste D3PieChartBridge</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .controls {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
      }
      .controls button {
        margin-right: 10px;
        padding: 8px 16px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      .controls button:hover {
        background: #0056b3;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🧪 Teste D3PieChartBridge</h1>

      <div class="controls">
        <h3><PERSON>es de Teste</h3>
        <button onclick="testBasicPie()">🥧 Pie Chart Básico</button>
        <button onclick="testDonutChart()">🍩 Donut Chart</button>
        <button onclick="testCustomColors()">🎨 Cores Customizadas</button>
        <button onclick="testPadding()">📐 Com Espaçamento</button>
        <button onclick="clearChart()">🧹 Limpar</button>
      </div>

      <div id="test-results">
        <h3>📊 Resultados dos Testes</h3>
        <div
          id="log-output"
          style="
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
          "
        >
          Aguardando testes...
        </div>
      </div>

      <hr style="margin: 30px 0" />

      <div id="visual-test">
        <h3>👁️ Teste Visual</h3>
        <p>
          Verifique se os gráficos estão sendo criados corretamente no projeto
          principal.
        </p>

        <div
          style="
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
          "
        >
          <h4>✅ Checklist de Testes:</h4>
          <ul>
            <li>[ ] Fatias criadas com tamanhos corretos</li>
            <li>[ ] Labels posicionados no centro das fatias</li>
            <li>[ ] Porcentagens calculadas corretamente</li>
            <li>[ ] Cores aplicadas corretamente</li>
            <li>[ ] Espaçamento entre fatias funcionando</li>
            <li>[ ] Conversão Pie ↔ Donut funcionando</li>
            <li>[ ] Interatividade mantida (arrastar, selecionar)</li>
          </ul>
        </div>

        <div
          style="
            background: #fff3e0;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
          "
        >
          <h4>🔧 Configurações D3.js Testadas:</h4>
          <ul>
            <li>
              <strong>padAngle:</strong> Espaçamento entre fatias (0-0.1 rad)
            </li>
            <li>
              <strong>cornerRadius:</strong> Arredondamento das bordas (0-10px)
            </li>
            <li>
              <strong>innerRadius:</strong> Raio interno para donut (0 = pie, >0
              = donut)
            </li>
            <li>
              <strong>useSVGLabels:</strong> Tipo de labels (DGM vs SVG puro)
            </li>
          </ul>
        </div>
      </div>
    </div>

    <script>
      function log(message) {
        const output = document.getElementById("log-output");
        const timestamp = new Date().toLocaleTimeString();
        output.innerHTML += `[${timestamp}] ${message}\n`;
        output.scrollTop = output.scrollHeight;
      }

      function testBasicPie() {
        log("🧪 Testando Pie Chart Básico...");
        log(
          "✅ Verifique no projeto principal se o gráfico de torta básico foi criado"
        );
        log("📋 Dados de teste: 5 fatias com valores [100, 200, 150, 75, 125]");
      }

      function testDonutChart() {
        log("🧪 Testando Donut Chart...");
        log("✅ Verifique se o gráfico tem um buraco no centro");
        log("📋 innerRadius deve ser > 0");
      }

      function testCustomColors() {
        log("🧪 Testando Cores Customizadas...");
        log("✅ Verifique se as cores das fatias são diferentes e vibrantes");
      }

      function testPadding() {
        log("🧪 Testando Espaçamento entre Fatias...");
        log("✅ Verifique se há espaços visíveis entre as fatias");
        log("📋 padAngle deve ser > 0");
      }

      function clearChart() {
        log("🧹 Limpando gráfico...");
        const output = document.getElementById("log-output");
        output.innerHTML = "Log limpo. Aguardando novos testes...\n";
      }

      // Log inicial
      log("🚀 Sistema de testes D3PieChartBridge carregado");
      log("📝 Use os botões acima para testar diferentes funcionalidades");
      log("👀 Monitore o console do navegador para logs detalhados");
    </script>
  </body>
</html>
