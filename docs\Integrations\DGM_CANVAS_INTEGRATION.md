# DGM Canvas Integration - Setup Guide

## 📊 **Visão Geral**

O projeto **app-calc-reino** agora possui integração completa com o **DGM Canvas** através de um módulo dedicado e modular. Aqui está como funciona:

## 🏗️ **Arquitetura da Integração**

### **Fluxo de Dados:**

```
Formulário → Validação → Supabase → DGM Canvas Integration → Seu Canvas
     ↓           ↓          ↓              ↓                    ↓
  Coleta      Confirma   Salva no     Formata dados        Envia para
  Dados       Dados      Banco        p/ Canvas            Visualização
```

### **Módulos Envolvidos:**

1. **`webflow-button-integration.js`** - Coleta dados do formulário
2. **`dgm-canvas-integration.js`** - **NOVO** - Módulo dedicado para DGM Canvas
3. **`app.js`** - Coordena a integração

## 🔧 **Configuração**

### **1. Endpoint Configuration**

No arquivo `app.js`, a configuração do endpoint é dinâmica:

```javascript
getDGMCanvasEndpoint() {
  const isDevelopment = 
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.search.includes('debug=true');

  if (isDevelopment) {
    return 'http://localhost:5173/api/data'; // DGM Canvas dev server
  }

  // Production endpoint
  return 'https://your-dgm-canvas-production-url.com/api/data';
}
```

### **2. Configuração Personalizada**

Você pode configurar a integração programaticamente:

```javascript
// Acesso global via ReinoCalculator
ReinoCalculator.systems.webflowButton.configureDGMCanvas({
  endpoint: 'http://sua-url-customizada.com/api/data',
  timeout: 45000, // 45 segundos
  retryAttempts: 5,
  retryDelay: 2000, // 2 segundos entre tentativas
  headers: {
    'Authorization': 'Bearer your-token',
    'X-Custom-Header': 'valor'
  }
});
```

## 📦 **Estrutura dos Dados Enviados**

O módulo `dgm-canvas-integration.js` formata os dados numa estrutura limpa e organizada:

```json
{
  "id": "supabase_id_ou_temp_id",
  "timestamp": "2025-01-04T10:30:00.000Z",
  
  "patrimonio": {
    "total": 100000,
    "alocado": 85000,
    "restante": 15000,
    "percentualAlocado": 85.00
  },
  
  "ativos": {
    "escolhidos": [
      { "product": "Tesouro Direto", "category": "renda-fixa" },
      { "product": "Ações", "category": "renda-variavel" }
    ],
    "alocacao": [
      {
        "key": "renda-fixa-tesouro-direto",
        "category": "renda-fixa",
        "product": "Tesouro Direto",
        "value": 50000,
        "percentage": 58.82,
        "formatted": {
          "value": "R$ 50.000,00",
          "percentage": "58.82%"
        }
      }
    ],
    "resumo": {
      "totalItems": 2,
      "totalValue": 85000,
      "categories": [
        {
          "name": "renda-fixa",
          "value": 50000,
          "count": 1,
          "percentage": 58.82
        }
      ]
    }
  },
  
  "usuario": {
    "nome": "João Silva",
    "email": "<EMAIL>",
    "hasUserData": true
  },
  
  "metadata": {
    "source": "app-calc-reino",
    "version": "1.0.0",
    "hasTypebot": true,
    "typebotSessionId": "session_123",
    "typebotResultId": "result_456",
    "userAgent": "Mozilla/5.0...",
    "sessionId": "temp_1709552400000_abc123",
    "supabaseId": "uuid-from-supabase",
    "submittedAt": "2025-01-04T10:30:00.000Z"
  }
}
```

## 🔄 **Como Receber no DGM Canvas**

### **1. Setup do Endpoint**

No seu projeto DGM Canvas (`dgm-canvas`), crie um endpoint para receber os dados:

```javascript
// No seu server.js ou vite config
app.post('/api/data', (req, res) => {
  const data = req.body;
  
  console.log('📊 Dados recebidos do app-calc-reino:', data);
  
  // Processar dados conforme necessário
  // Atualizar visualizações
  // Salvar em estado global, etc.
  
  res.json({ 
    success: true, 
    message: 'Dados recebidos com sucesso',
    receivedAt: new Date().toISOString()
  });
});
```

### **2. Exemplo de Processamento**

```javascript
// Exemplo de como usar os dados
function processReinoData(data) {
  // Atualizar gráficos
  updatePatrimonioChart(data.patrimonio);
  
  // Atualizar tabela de ativos
  updateAssetsTable(data.ativos.alocacao);
  
  // Mostrar informações do usuário
  if (data.usuario.hasUserData) {
    displayUserInfo(data.usuario);
  }
  
  // Log de metadados
  console.log('Fonte:', data.metadata.source);
  console.log('Typebot usado:', data.metadata.hasTypebot);
}
```

## 🛡️ **Features do Módulo**

### **Retry Logic**

- **3 tentativas por padrão**
- **Delay progressivo** (1s, 2s, 3s)
- **Timeouts configuráveis**

### **Error Handling**

- **Não falha o envio principal** se DGM Canvas estiver offline
- **Logs detalhados** para debugging
- **Events customizados** para monitoramento

### **Debug Mode**

- **Logs detalhados** em desenvolvimento
- **Connection testing** automático
- **Status monitoring**

## 🎯 **Eventos Disponíveis**

```javascript
// Sucesso
document.addEventListener('dgmCanvasDataSent', (event) => {
  console.log('✅ Dados enviados:', event.detail);
});

// Erro
document.addEventListener('dgmCanvasError', (event) => {
  console.error('❌ Erro no envio:', event.detail);
});
```

## 🔍 **Debugging**

### **Status da Integração**

```javascript
// Via console do navegador
const status = ReinoCalculator.systems.webflowButton.getDGMCanvasStatus();
console.log('Status DGM Canvas:', status);
```

### **Teste Manual**

```javascript
// Enviar dados de teste
ReinoCalculator.systems.webflowButton.configureDGMCanvas({
  endpoint: 'http://localhost:5173/api/test'
});
```

## 🚀 **Próximos Passos**

1. **Configure o endpoint** no seu DGM Canvas
2. **Teste a integração** em desenvolvimento
3. **Ajuste a URL de produção** conforme necessário
4. **Implemente a visualização** dos dados recebidos

---

**✅ A integração está pronta e funcional!** Os dados do app-calc-reino serão automaticamente enviados para o seu DGM Canvas sempre que um usuário completar o formulário.
