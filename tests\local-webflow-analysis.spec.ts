import { expect, test } from '@playwright/test';
import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * An<PERSON>lise da implementação local do Webflow
 * Pasta: C:\Users\<USER>\Desktop\Integração Reino\app-calc-reino\Modelo - Webflow
 */
test.describe('Local Webflow Analysis', () => {
  const webflowPath =
    'c:\\Users\\<USER>\\Desktop\\Integração Reino\\app-calc-reino\\Modelo - Webflow';

  test('should analyze local Webflow structure', async ({ page }) => {
    console.log('🔍 Analyzing local Webflow implementation...');

    // Navigate to local file
    const indexPath = `file:///${webflowPath}\\index.html`;
    console.log('Loading:', indexPath);

    await page.goto(indexPath);
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(1000);

    console.log('\n📊 === LOCAL WEBFLOW STRUCTURE ANALYSIS ===');

    // Analyze page structure
    const localStructure = await page.evaluate(() => {
      const structure = {
        title: document.title,
        sections: [],
        stepNavigation: {
          hasStepNavigation: !!document.querySelector('.step-navigation'),
          hasStepSections: document.querySelectorAll('.step-section').length,
          sectionsWithDataStep: document.querySelectorAll('[data-step]').length,
          activeSection: document.querySelector('.step-section.active')?.className || 'none',
          allStepSections: Array.from(document.querySelectorAll('.step-section')).map((el) => ({
            className: el.className,
            dataStep: el.getAttribute('data-step'),
            id: el.id || 'no-id',
            visible: getComputedStyle(el).display !== 'none',
            height: el.getBoundingClientRect().height,
          })),
        },
        targetSections: {
          intro: !!document.querySelector('._0-home-section-calc-intro'),
          money: !!document.querySelector('._1-section-calc-money'),
          assets: !!document.querySelector('._2-section-calc-ativos'),
          allocation: !!document.querySelector('._3-section-patrimonio-alocation'),
        },
        scrollInfo: {
          documentHeight: document.documentElement.scrollHeight,
          viewportHeight: window.innerHeight,
          isScrollable: document.documentElement.scrollHeight > window.innerHeight,
        },
      };

      // Analyze all sections
      const sections = document.querySelectorAll(
        'section, div[class*="section"], [class*="_section"]'
      );
      sections.forEach((section, index) => {
        const rect = section.getBoundingClientRect();
        const styles = getComputedStyle(section);

        structure.sections.push({
          index,
          tagName: section.tagName,
          className: section.className,
          id: section.id || '',
          height: rect.height,
          display: styles.display,
          position: styles.position,
          visible: rect.height > 0 && styles.display !== 'none',
          hasStepClass: section.classList.contains('step-section'),
          dataStep: section.getAttribute('data-step'),
          zIndex: styles.zIndex !== 'auto' ? styles.zIndex : 'auto',
        });
      });

      return structure;
    });

    console.log('Page Title:', localStructure.title);
    console.log('Document Height:', localStructure.scrollInfo.documentHeight);
    console.log('Is Scrollable:', localStructure.scrollInfo.isScrollable);

    console.log('\n🎯 === STEP NAVIGATION STATUS ===');
    console.log('Step Navigation Present:', localStructure.stepNavigation.hasStepNavigation);
    console.log('Step Sections Found:', localStructure.stepNavigation.hasStepSections);
    console.log('Sections with data-step:', localStructure.stepNavigation.sectionsWithDataStep);
    console.log('Active Section:', localStructure.stepNavigation.activeSection);

    console.log('\n📋 === TARGET SECTIONS STATUS ===');
    console.log(
      'Intro Section (._0-home-section-calc-intro):',
      localStructure.targetSections.intro
    );
    console.log('Money Section (._1-section-calc-money):', localStructure.targetSections.money);
    console.log('Assets Section (._2-section-calc-ativos):', localStructure.targetSections.assets);
    console.log(
      'Allocation Section (._3-section-patrimonio-alocation):',
      localStructure.targetSections.allocation
    );

    console.log('\n📐 === ALL SECTIONS ANALYSIS ===');
    localStructure.sections.forEach((section, index) => {
      console.log(`\nSection ${index + 1}:`);
      console.log(`  - Tag: ${section.tagName}`);
      console.log(
        `  - Class: ${section.className.substring(0, 60)}${section.className.length > 60 ? '...' : ''}`
      );
      console.log(`  - Height: ${section.height}px`);
      console.log(`  - Display: ${section.display}`);
      console.log(`  - Visible: ${section.visible}`);
      if (section.hasStepClass) {
        console.log(`  - ✅ HAS step-section class`);
      }
      if (section.dataStep) {
        console.log(`  - ✅ data-step: ${section.dataStep}`);
      }
      if (section.zIndex !== 'auto') {
        console.log(`  - Z-Index: ${section.zIndex}`);
      }
    });

    console.log('\n🔧 === STEP SECTIONS DETAILS ===');
    localStructure.stepNavigation.allStepSections.forEach((section, index) => {
      console.log(`Step Section ${index + 1}:`);
      console.log(`  - Class: ${section.className}`);
      console.log(`  - data-step: ${section.dataStep || 'MISSING'}`);
      console.log(`  - Visible: ${section.visible}`);
      console.log(`  - Height: ${section.height}px`);
    });

    // Test scroll behavior on local file
    console.log('\n📜 === SCROLL BEHAVIOR TEST ===');

    const scrollTest = await page.evaluate(() => {
      const originalHeight = document.documentElement.scrollHeight;

      // Try to scroll
      window.scrollTo(0, 100);
      const scrolledPosition = window.scrollY;

      // Check if any sections respond to scroll
      const visibleSections = Array.from(document.querySelectorAll('section, [class*="_section"]'))
        .filter((el) => {
          const rect = el.getBoundingClientRect();
          return rect.height > 0 && getComputedStyle(el).display !== 'none';
        })
        .map((el) => ({
          className: el.className.split(' ')[0],
          height: el.getBoundingClientRect().height,
        }));

      return {
        originalHeight,
        scrolledPosition,
        visibleSections,
        canScroll: originalHeight > window.innerHeight,
      };
    });

    console.log('Original document height:', scrollTest.originalHeight);
    console.log('Scrolled to position:', scrollTest.scrolledPosition);
    console.log('Can scroll:', scrollTest.canScroll);
    console.log('Visible sections:', scrollTest.visibleSections.length);

    if (scrollTest.visibleSections.length > 0) {
      console.log('\nVisible sections:');
      scrollTest.visibleSections.forEach((section) => {
        console.log(`  - ${section.className}: ${section.height}px`);
      });
    }

    // Check for any CSS that might be hiding content
    console.log('\n🎨 === CSS ANALYSIS ===');

    const cssAnalysis = await page.evaluate(() => {
      const analysis = {
        bodyOverflow: getComputedStyle(document.body).overflow,
        htmlOverflow: getComputedStyle(document.documentElement).overflow,
        bodyHeight: getComputedStyle(document.body).height,
        stepSectionsCSS: [],
      };

      // Check step-section CSS
      const stepSections = document.querySelectorAll('.step-section');
      stepSections.forEach((section, index) => {
        const styles = getComputedStyle(section);
        analysis.stepSectionsCSS.push({
          index,
          display: styles.display,
          height: styles.height,
          minHeight: styles.minHeight,
          maxHeight: styles.maxHeight,
          opacity: styles.opacity,
          visibility: styles.visibility,
          transform: styles.transform,
        });
      });

      return analysis;
    });

    console.log('Body overflow:', cssAnalysis.bodyOverflow);
    console.log('HTML overflow:', cssAnalysis.htmlOverflow);
    console.log('Body height:', cssAnalysis.bodyHeight);

    if (cssAnalysis.stepSectionsCSS.length > 0) {
      console.log('\nStep sections CSS:');
      cssAnalysis.stepSectionsCSS.forEach((css, index) => {
        console.log(`  Section ${index + 1}:`);
        console.log(`    - display: ${css.display}`);
        console.log(`    - height: ${css.height}`);
        console.log(`    - minHeight: ${css.minHeight}`);
        console.log(`    - opacity: ${css.opacity}`);
        if (css.transform !== 'none') {
          console.log(`    - transform: ${css.transform}`);
        }
      });
    }

    // Take screenshot
    await page.screenshot({
      path: 'test-results/local-webflow-analysis.png',
      fullPage: true,
    });

    console.log('\n✅ === LOCAL ANALYSIS COMPLETE ===');
    console.log('📸 Screenshot saved to test-results/local-webflow-analysis.png');

    // Validate expected structure
    expect(localStructure.targetSections.intro).toBe(true);
    expect(localStructure.targetSections.money).toBe(true);
    expect(localStructure.targetSections.assets).toBe(true);
    expect(localStructure.targetSections.allocation).toBe(true);
  });
});
