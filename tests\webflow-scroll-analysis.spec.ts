import { expect, Page, test } from '@playwright/test';

/**
 * Análise do comportamento de scroll no site Webflow
 * https://grupos-groovy-site-fc802f.webflow.io/
 */
test.describe('Webflow Scroll Analysis', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();

    // Monitor console logs
    page.on('console', (msg) => {
      console.log(`[CONSOLE ${msg.type()}]:`, msg.text());
    });

    // Monitor network requests
    page.on('request', (request) => {
      if (request.url().includes('webflow') || request.url().includes('scroll')) {
        console.log(`[REQUEST]: ${request.method()} ${request.url()}`);
      }
    });
  });

  test('should analyze scroll behavior and page structure', async () => {
    console.log('🔍 Starting Webflow scroll analysis...');

    // Navigate to the Webflow site
    await page.goto('https://grupos-groovy-site-fc802f.webflow.io/');
    await page.waitForLoadState('networkidle');

    // Wait for potential animations to settle
    await page.waitForTimeout(2000);

    console.log('\n📊 === PAGE STRUCTURE ANALYSIS ===');

    // Analyze page structure
    const pageStructure = await page.evaluate(() => {
      const structure = {
        title: document.title,
        bodyClasses: document.body.className,
        sections: [],
        scrollInfo: {
          documentHeight: document.documentElement.scrollHeight,
          viewportHeight: window.innerHeight,
          scrollTop: window.scrollY,
          isScrollable: document.documentElement.scrollHeight > window.innerHeight,
        },
        webflowElements: {
          webflowScript: !!document.querySelector('script[src*="webflow"]'),
          webflowCSS: !!document.querySelector('link[href*="webflow"]'),
          webflowClasses: Array.from(document.querySelectorAll('[class*="w-"]')).length,
        },
      };

      // Analyze sections
      const sections = document.querySelectorAll(
        'section, div[class*="section"], [class*="_section"]'
      );
      sections.forEach((section, index) => {
        const rect = section.getBoundingClientRect();
        structure.sections.push({
          index,
          tagName: section.tagName,
          className: section.className,
          id: section.id || '',
          height: rect.height,
          top: rect.top + window.scrollY,
          visible: rect.top < window.innerHeight && rect.bottom > 0,
          hasFixedPosition: getComputedStyle(section).position === 'fixed',
          zIndex: getComputedStyle(section).zIndex,
        });
      });

      return structure;
    });

    console.log('Page Title:', pageStructure.title);
    console.log('Body Classes:', pageStructure.bodyClasses);
    console.log('Scroll Info:', pageStructure.scrollInfo);
    console.log('Webflow Elements:', pageStructure.webflowElements);
    console.log(`Found ${pageStructure.sections.length} sections`);

    // Log section details
    pageStructure.sections.forEach((section, index) => {
      console.log(`\nSection ${index + 1}:`);
      console.log(`  - Tag: ${section.tagName}`);
      console.log(
        `  - Class: ${section.className.substring(0, 80)}${section.className.length > 80 ? '...' : ''}`
      );
      console.log(`  - Height: ${section.height}px`);
      console.log(`  - Position: top=${section.top}px`);
      console.log(`  - Visible: ${section.visible}`);
      console.log(`  - Fixed: ${section.hasFixedPosition}`);
      if (section.zIndex !== 'auto') {
        console.log(`  - Z-Index: ${section.zIndex}`);
      }
    });

    console.log('\n🎯 === SCROLL BEHAVIOR ANALYSIS ===');

    // Test scroll behavior
    const scrollSteps = [0, 0.25, 0.5, 0.75, 1];

    for (const step of scrollSteps) {
      const scrollPosition = Math.floor(pageStructure.scrollInfo.documentHeight * step);

      console.log(`\n📍 Scrolling to ${(step * 100).toFixed(0)}% (${scrollPosition}px)...`);

      await page.evaluate((pos) => {
        window.scrollTo({ top: pos, behavior: 'smooth' });
      }, scrollPosition);

      // Wait for scroll to complete
      await page.waitForTimeout(1000);

      // Analyze what's visible at this scroll position
      const visibleElements = await page.evaluate(() => {
        const viewport = {
          top: window.scrollY,
          bottom: window.scrollY + window.innerHeight,
        };

        const visible = [];
        const sections = document.querySelectorAll(
          'section, div[class*="section"], [class*="_section"]'
        );

        sections.forEach((section, index) => {
          const rect = section.getBoundingClientRect();
          const elementTop = rect.top + window.scrollY;
          const elementBottom = elementTop + rect.height;

          if (elementBottom > viewport.top && elementTop < viewport.bottom) {
            const visiblePercentage = Math.max(
              0,
              Math.min(
                100,
                ((Math.min(elementBottom, viewport.bottom) - Math.max(elementTop, viewport.top)) /
                  rect.height) *
                  100
              )
            );

            visible.push({
              index,
              className: section.className.split(' ')[0] || section.tagName,
              visiblePercentage: Math.round(visiblePercentage),
              top: elementTop,
              height: rect.height,
            });
          }
        });

        return {
          scrollY: window.scrollY,
          visibleSections: visible,
        };
      });

      console.log(`Current scroll: ${visibleElements.scrollY}px`);
      visibleElements.visibleSections.forEach((section) => {
        console.log(`  - ${section.className}: ${section.visiblePercentage}% visible`);
      });
    }

    console.log('\n🔧 === TECHNICAL ANALYSIS ===');

    // Check for specific scroll-related elements
    const scrollTechInfo = await page.evaluate(() => {
      const info = {
        smoothScrollSupported: CSS.supports('scroll-behavior', 'smooth'),
        hasOverflowHidden: false,
        fixedElements: [],
        stickyElements: [],
        scrollListeners: 0,
        webflowAnimations: [],
        customScrollElements: [],
      };

      // Check for overflow hidden on body/html
      const bodyStyle = getComputedStyle(document.body);
      const htmlStyle = getComputedStyle(document.documentElement);
      info.hasOverflowHidden = bodyStyle.overflow === 'hidden' || htmlStyle.overflow === 'hidden';

      // Find fixed and sticky elements
      const allElements = document.querySelectorAll('*');
      allElements.forEach((el) => {
        const style = getComputedStyle(el);
        if (style.position === 'fixed') {
          info.fixedElements.push({
            tagName: el.tagName,
            className: el.className.split(' ')[0] || 'no-class',
            zIndex: style.zIndex,
          });
        }
        if (style.position === 'sticky') {
          info.stickyElements.push({
            tagName: el.tagName,
            className: el.className.split(' ')[0] || 'no-class',
          });
        }
      });

      // Look for Webflow animations
      const wAnimations = document.querySelectorAll('[data-w-id]');
      wAnimations.forEach((el) => {
        info.webflowAnimations.push({
          id: el.getAttribute('data-w-id'),
          className: el.className.split(' ')[0] || el.tagName,
        });
      });

      // Look for custom scroll elements
      const scrollElements = document.querySelectorAll('[class*="scroll"], [data-scroll]');
      scrollElements.forEach((el) => {
        info.customScrollElements.push({
          className: el.className,
          dataScroll: el.getAttribute('data-scroll') || 'none',
        });
      });

      return info;
    });

    console.log('Smooth scroll supported:', scrollTechInfo.smoothScrollSupported);
    console.log('Has overflow hidden:', scrollTechInfo.hasOverflowHidden);
    console.log('Fixed elements:', scrollTechInfo.fixedElements.length);
    console.log('Sticky elements:', scrollTechInfo.stickyElements.length);
    console.log('Webflow animations:', scrollTechInfo.webflowAnimations.length);
    console.log('Custom scroll elements:', scrollTechInfo.customScrollElements.length);

    if (scrollTechInfo.fixedElements.length > 0) {
      console.log('\nFixed Elements:');
      scrollTechInfo.fixedElements.forEach((el) => {
        console.log(`  - ${el.tagName}.${el.className} (z-index: ${el.zIndex})`);
      });
    }

    if (scrollTechInfo.webflowAnimations.length > 0) {
      console.log('\nWebflow Animations:');
      scrollTechInfo.webflowAnimations.slice(0, 5).forEach((anim) => {
        console.log(`  - ID: ${anim.id} on ${anim.className}`);
      });
      if (scrollTechInfo.webflowAnimations.length > 5) {
        console.log(`  ... and ${scrollTechInfo.webflowAnimations.length - 5} more`);
      }
    }

    console.log('\n🎭 === SCROLL PERFORMANCE TEST ===');

    // Test scroll performance
    const performanceMetrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        const metrics = {
          startTime: performance.now(),
          scrollEvents: 0,
          frameDrops: 0,
          smoothness: 0,
        };

        let lastScrollTime = performance.now();
        const scrollHandler = () => {
          const now = performance.now();
          metrics.scrollEvents++;

          // Check for frame drops (> 16.67ms between events indicates dropped frames)
          if (now - lastScrollTime > 20) {
            metrics.frameDrops++;
          }
          lastScrollTime = now;
        };

        window.addEventListener('scroll', scrollHandler);

        // Perform rapid scroll test
        let currentScroll = 0;
        const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
        const scrollStep = maxScroll / 20;

        const scrollInterval = setInterval(() => {
          currentScroll += scrollStep;
          if (currentScroll >= maxScroll) {
            clearInterval(scrollInterval);
            window.removeEventListener('scroll', scrollHandler);

            metrics.smoothness = Math.max(
              0,
              100 - (metrics.frameDrops / metrics.scrollEvents) * 100
            );
            metrics.endTime = performance.now();
            metrics.totalTime = metrics.endTime - metrics.startTime;

            resolve(metrics);
          } else {
            window.scrollTo(0, currentScroll);
          }
        }, 50);
      });
    });

    console.log('Scroll Performance Metrics:');
    console.log(`  - Total scroll events: ${performanceMetrics.scrollEvents}`);
    console.log(`  - Frame drops detected: ${performanceMetrics.frameDrops}`);
    console.log(`  - Smoothness score: ${performanceMetrics.smoothness.toFixed(1)}%`);
    console.log(`  - Test duration: ${performanceMetrics.totalTime.toFixed(0)}ms`);

    console.log('\n✅ === ANALYSIS COMPLETE ===');

    // Scroll back to top
    await page.evaluate(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    // Take a screenshot for reference
    await page.screenshot({
      path: 'test-results/webflow-scroll-analysis.png',
      fullPage: true,
    });

    console.log('📸 Full page screenshot saved to test-results/webflow-scroll-analysis.png');
  });

  test('should compare with local implementation', async () => {
    console.log('\n🔄 === COMPARING WITH LOCAL IMPLEMENTATION ===');

    // Test local version if available
    try {
      await page.goto('http://localhost:3000');
      await page.waitForLoadState('networkidle');

      const localStructure = await page.evaluate(() => {
        return {
          hasStepNavigation: !!document.querySelector('.step-navigation'),
          hasStepSections: document.querySelectorAll('.step-section').length,
          sectionsWithDataStep: document.querySelectorAll('[data-step]').length,
          activeSection: document.querySelector('.step-section.active')?.className || 'none',
        };
      });

      console.log('Local Implementation Analysis:');
      console.log('  - Step navigation present:', localStructure.hasStepNavigation);
      console.log('  - Step sections found:', localStructure.hasStepSections);
      console.log('  - Sections with data-step:', localStructure.sectionsWithDataStep);
      console.log('  - Active section:', localStructure.activeSection);
    } catch (error) {
      console.log('Local server not available for comparison');
    }
  });
});
