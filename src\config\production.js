/**
 * Environment Configuration for Production Deployment
 * DGM Canvas + app-calc-reino Integration
 */

export const PRODUCTION_CONFIG = {
  // API Configuration
  api: {
    historyEndpoint: "/api/history",
    pollInterval: 5000, // 5 seconds in production
    timeout: 10000, // 10 seconds timeout
    retryAttempts: 3,
    retryDelay: 1000,
  },

  // Security
  security: {
    enableRateLimit: true,
    maxRequestsPerMinute: 60,
    enableCORS: true,
    allowedOrigins: [
      "https://dgm-canvas.seu-dominio.com",
      "https://seu-dominio.com",
    ],
  },

  // Performance
  performance: {
    enableCache: true,
    cacheTimeout: 300, // 5 minutes
    maxCacheSize: 100, // Max cached items
    enableCompression: true,
  },

  // Monitoring
  monitoring: {
    enableHealthChecks: true,
    healthCheckInterval: 30000, // 30 seconds
    enableMetrics: true,
    logLevel: "info",
  },

  // Database
  database: {
    maxRetries: 3,
    retryDelay: 2000,
    connectionTimeout: 10000,
  },
};

export const DEVELOPMENT_CONFIG = {
  api: {
    historyEndpoint: "http://localhost:3001/api/history",
    pollInterval: 2000,
    timeout: 5000,
    retryAttempts: 1,
    retryDelay: 500,
  },

  security: {
    enableRateLimit: false,
    maxRequestsPerMinute: 1000,
    enableCORS: true,
    allowedOrigins: ["http://localhost:3000", "http://localhost:5173"],
  },

  performance: {
    enableCache: false,
    cacheTimeout: 60,
    maxCacheSize: 10,
    enableCompression: false,
  },

  monitoring: {
    enableHealthChecks: true,
    healthCheckInterval: 10000,
    enableMetrics: false,
    logLevel: "debug",
  },

  database: {
    maxRetries: 1,
    retryDelay: 1000,
    connectionTimeout: 5000,
  },
};

/**
 * Get configuration based on environment
 */
export const getConfig = () => {
  const env = import.meta?.env?.MODE || process.env.NODE_ENV || "development";

  if (env === "production") {
    return PRODUCTION_CONFIG;
  }

  return DEVELOPMENT_CONFIG;
};

/**
 * Validate configuration
 */
export const validateConfig = (config) => {
  const required = [
    "api.historyEndpoint",
    "api.pollInterval",
    "security.allowedOrigins",
  ];

  for (const path of required) {
    const value = path.split(".").reduce((obj, key) => obj?.[key], config);
    if (value === undefined || value === null) {
      throw new Error(`Missing required configuration: ${path}`);
    }
  }

  return true;
};

export default {
  getConfig,
  validateConfig,
  PRODUCTION_CONFIG,
  DEVELOPMENT_CONFIG,
};
