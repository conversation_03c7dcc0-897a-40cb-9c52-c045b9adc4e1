import { expect, test } from '@playwright/test';

/**
 * Teste específico para validar fix do scroll graneed
 */
test.describe('Step Navigation Fix Validation', () => {
  test('should show only one section at a time', async ({ page }) => {
    console.log('🔍 Testing step navigation fix...');

    // Load local file with updated JavaScript
    const indexPath = `file:///c:\\Users\\<USER>\\Desktop\\Integração Reino\\app-calc-reino\\Modelo - Webflow\\index.html`;
    await page.goto(indexPath);
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(2000); // Wait for step navigation to initialize

    console.log('\n📊 === AFTER FIX ANALYSIS ===');

    // Check current state
    const stepState = await page.evaluate(() => {
      const sections = Array.from(document.querySelectorAll('.step-section'));

      return {
        totalSections: sections.length,
        visibleSections: sections.filter((s) => {
          const styles = getComputedStyle(s);
          return styles.display !== 'none' && styles.opacity !== '0';
        }).length,
        activeSections: sections.filter((s) => s.classList.contains('active')).length,
        sectionStates: sections.map((section, index) => {
          const styles = getComputedStyle(section);
          return {
            index,
            className: section.className,
            display: styles.display,
            opacity: styles.opacity,
            height: section.getBoundingClientRect().height,
            isActive: section.classList.contains('active'),
            dataStepMethod: section.getAttribute('data-step-method'),
          };
        }),
        documentHeight: document.documentElement.scrollHeight,
        viewportHeight: window.innerHeight,
        isScrollable: document.documentElement.scrollHeight > window.innerHeight,
      };
    });

    console.log('Total sections:', stepState.totalSections);
    console.log('Visible sections:', stepState.visibleSections);
    console.log('Active sections:', stepState.activeSections);
    console.log('Document height:', stepState.documentHeight);
    console.log('Is scrollable:', stepState.isScrollable);

    console.log('\nSection states:');
    stepState.sectionStates.forEach((section, index) => {
      console.log(`  Section ${index + 1}:`);
      console.log(`    - Class: ${section.className.split(' ')[0]}`);
      console.log(`    - Display: ${section.display}`);
      console.log(`    - Opacity: ${section.opacity}`);
      console.log(`    - Height: ${section.height}px`);
      console.log(`    - Active: ${section.isActive}`);
      console.log(`    - Method: ${section.dataStepMethod}`);
    });

    // Test navigation
    console.log('\n🎯 === TESTING STEP NAVIGATION ===');

    // Check if step navigation exists and is functional
    const hasStepNav = await page.evaluate(() => {
      return !!document.querySelector('.step-navigation');
    });

    console.log('Step navigation present:', hasStepNav);

    if (hasStepNav) {
      // Try to navigate to next step
      console.log('Testing navigation to next step...');

      await page.evaluate(() => {
        const nextBtn = document.querySelector('.next-btn');
        if (nextBtn && !nextBtn.disabled) {
          nextBtn.click();
        }
      });

      await page.waitForTimeout(500);

      // Check state after navigation
      const afterNavigation = await page.evaluate(() => {
        const sections = Array.from(document.querySelectorAll('.step-section'));
        return {
          visibleSections: sections.filter((s) => {
            const styles = getComputedStyle(s);
            return styles.display !== 'none' && styles.opacity !== '0';
          }).length,
          activeSections: sections.filter((s) => s.classList.contains('active')).length,
          documentHeight: document.documentElement.scrollHeight,
        };
      });

      console.log('After navigation:');
      console.log('  - Visible sections:', afterNavigation.visibleSections);
      console.log('  - Active sections:', afterNavigation.activeSections);
      console.log('  - Document height:', afterNavigation.documentHeight);

      // Assertions
      expect(afterNavigation.visibleSections).toBeLessThanOrEqual(1);
      expect(afterNavigation.activeSections).toBeLessThanOrEqual(1);
    }

    // Take screenshot for visual verification
    await page.screenshot({
      path: 'test-results/step-navigation-fix-validation.png',
      fullPage: true,
    });

    console.log('\n✅ === FIX VALIDATION COMPLETE ===');
    console.log('📸 Screenshot saved to test-results/step-navigation-fix-validation.png');

    // Final assertion - should have only one visible section or be non-scrollable
    expect(stepState.visibleSections).toBeLessThanOrEqual(1);
  });
});
