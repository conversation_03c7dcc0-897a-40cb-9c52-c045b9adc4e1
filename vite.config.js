import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Plugin customizado para lidar com API routes
    {
      name: "dgm-api-routes",
      configureServer(server) {
        // Middleware para receber dados do app-calc-reino
        server.middlewares.use("/api/data", (req, res, next) => {
          if (req.method === "POST") {
            let body = "";

            req.on("data", (chunk) => {
              body += chunk.toString();
            });

            req.on("end", () => {
              try {
                const data = JSON.parse(body);

                console.log(
                  "📊 [DGM Canvas] Dados recebidos do app-calc-reino:"
                );
                console.log("🏦 Patrimônio:", data.patrimonio);
                console.log("📈 Ativos:", data.ativos);
                console.log("👤 Usuário:", data.usuario);
                console.log("📋 Metadata:", data.metadata);

                // Broadcast para o frontend via WebSocket/SSE (opcional)
                // Aqui você pode implementar WebSocket para atualizar o canvas em tempo real

                // Salvar dados globalmente para acesso pelo frontend
                global.lastReinoData = data;
                global.receivedAt = new Date().toISOString();

                // Responder sucesso
                res.setHeader("Content-Type", "application/json");
                res.setHeader("Access-Control-Allow-Origin", "*");
                res.setHeader(
                  "Access-Control-Allow-Methods",
                  "POST, GET, OPTIONS"
                );
                res.setHeader(
                  "Access-Control-Allow-Headers",
                  "Content-Type, X-Source, X-Version"
                );

                res.statusCode = 200;
                res.end(
                  JSON.stringify({
                    success: true,
                    message: "Dados recebidos com sucesso no DGM Canvas!",
                    receivedAt: global.receivedAt,
                    dataId: data.id,
                  })
                );
              } catch (error) {
                console.error(
                  "❌ [DGM Canvas] Erro ao processar dados:",
                  error
                );
                res.statusCode = 400;
                res.end(
                  JSON.stringify({
                    success: false,
                    error: "Dados inválidos",
                  })
                );
              }
            });
          } else if (req.method === "OPTIONS") {
            // Handle CORS preflight
            res.setHeader("Access-Control-Allow-Origin", "*");
            res.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
            res.setHeader(
              "Access-Control-Allow-Headers",
              "Content-Type, X-Source, X-Version"
            );
            res.statusCode = 200;
            res.end();
          } else {
            next();
          }
        });

        // Endpoint para health check
        server.middlewares.use("/api/health", (req, res, next) => {
          if (req.method === "GET") {
            res.setHeader("Content-Type", "application/json");
            res.setHeader("Access-Control-Allow-Origin", "*");
            res.statusCode = 200;
            res.end(
              JSON.stringify({
                status: "ok",
                service: "dgm-canvas",
                timestamp: new Date().toISOString(),
              })
            );
          } else {
            next();
          }
        });

        // Endpoint para buscar últimos dados recebidos
        server.middlewares.use("/api/latest", (req, res, next) => {
          if (req.method === "GET") {
            res.setHeader("Content-Type", "application/json");
            res.setHeader("Access-Control-Allow-Origin", "*");

            // Silent endpoint - only log significant events
            const hasData = !!global.lastReinoData;
            if (
              hasData &&
              global.lastReinoData.timestamp &&
              (!global.lastLoggedData ||
                global.lastLoggedData !== global.lastReinoData.timestamp)
            ) {
              console.log("🔍 [DGM Canvas] New data served");
              global.lastLoggedData = global.lastReinoData.timestamp;
            }

            res.statusCode = 200;
            res.end(
              JSON.stringify({
                data: global.lastReinoData || null,
                receivedAt: global.receivedAt || null,
              })
            );
          } else {
            next();
          }
        });

        // Endpoint para debug logs do frontend - silent mode
        server.middlewares.use("/api/debug-log", (req, res, next) => {
          if (req.method === "POST") {
            let body = "";

            req.on("data", (chunk) => {
              body += chunk.toString();
            });

            req.on("end", () => {
              try {
                const logData = JSON.parse(body);
                // Only log critical errors, silence info logs
                if (logData.level === "error") {
                  console.error(`❌ [Frontend Debug] ${logData.message}`);
                  if (logData.data) {
                    console.error(`📋 [Frontend Debug] Data:`, logData.data);
                  }
                }

                res.setHeader("Content-Type", "application/json");
                res.setHeader("Access-Control-Allow-Origin", "*");
                res.statusCode = 200;
                res.end(JSON.stringify({ success: true }));
              } catch (error) {
                res.statusCode = 400;
                res.end(JSON.stringify({ success: false }));
              }
            });
          } else if (req.method === "OPTIONS") {
            res.setHeader("Access-Control-Allow-Origin", "*");
            res.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
            res.setHeader("Access-Control-Allow-Headers", "Content-Type");
            res.statusCode = 200;
            res.end();
          } else {
            next();
          }
        });
      },
    },
  ],
  server: {
    port: 5173,
    host: true, // Permite acesso externo
    cors: true,
  },
});
