# 🗄️ Supabase Integration Guide

## 📋 Pré-requisitos

1. **Conta no Supabase**: Crie uma conta em [supabase.com](https://supabase.com)
2. **Projeto criado**: Crie um novo projeto no Supabase
3. **Dependências instaladas**: Execute `pnpm install` para instalar o cliente Supabase

## 🚀 Configuração Passo a Passo

### 1. Configurar o Banco de Dados

1. **Acesse o SQL Editor** no painel do Supabase
2. **Execute o SQL de criação** localizado em `database/supabase-setup.sql`
3. **Verifique se a tabela foi criada** em "Table Editor"

### 2. Obter Credenciais do Projeto

1. **Vá para Settings > API** no painel do Supabase
2. **Copie os valores**:
   - **Project URL**: `https://xxxxxxxxxxxxx.supabase.co`
   - **Anon key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 3. Configurar as Credenciais

Edite o arquivo `src/config/supabase.js`:

```javascript
// Substitua estas linhas com suas credenciais reais
const SUPABASE_URL = 'https://seu-projeto.supabase.co';
const SUPABASE_ANON_KEY = 'sua-anon-key-aqui';
```

### 4. Testar a Integração

1. **Execute o projeto**:

   ```bash
   pnpm dev
   ```

2. **Preencha a calculadora** e clique em "Enviar"

3. **Verifique no Supabase** se os dados foram salvos na tabela `calculator_submissions`

## 📊 Estrutura dos Dados Salvos

### Campos Principais

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `patrimonio` | numeric | Valor total do patrimônio |
| `ativos_escolhidos` | jsonb | Array de ativos selecionados |
| `alocacao` | jsonb | Objeto com alocação detalhada |
| `total_alocado` | numeric | Soma dos valores alocados |
| `percentual_alocado` | numeric | % do patrimônio alocado |
| `patrimonio_restante` | numeric | Valor não alocado |

### Exemplo de Dados Salvos

```json
{
  "patrimonio": 500000,
  "ativos_escolhidos": [
    { "product": "tesouro-direto", "category": "renda-fixa" },
    { "product": "acoes-brasil", "category": "renda-variavel" }
  ],
  "alocacao": {
    "renda-fixa-tesouro-direto": {
      "value": 200000,
      "percentage": 40,
      "category": "renda-fixa",
      "product": "tesouro-direto"
    },
    "renda-variavel-acoes-brasil": {
      "value": 150000,
      "percentage": 30,
      "category": "renda-variavel", 
      "product": "acoes-brasil"
    }
  },
  "total_alocado": 350000,
  "percentual_alocado": 70,
  "patrimonio_restante": 150000
}
```

## 🔧 Configurações Opcionais

### Row Level Security (RLS)

Se quiser controlar o acesso aos dados, descomente as políticas no arquivo SQL:

```sql
ALTER TABLE calculator_submissions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow public inserts" ON calculator_submissions
  FOR INSERT WITH CHECK (true);
```

### Autenticação de Usuários

Para implementar autenticação:

1. **Configure Auth** no Supabase
2. **Adicione session tracking** baseado em usuários
3. **Implemente políticas RLS** para acesso por usuário

## 🐛 Troubleshooting

### ❌ Erro: "new row violates row-level security policy"

**Causa**: Row Level Security (RLS) está ativado mas não há políticas que permitam inserções.

**Solução Rápida**:

1. Vá para o **SQL Editor** no painel do Supabase
2. Execute o arquivo `database/quick-fix-rls.sql`
3. Ou execute este comando diretamente:

```sql
-- Criar política para permitir inserções públicas
ALTER TABLE calculator_submissions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow public inserts" ON calculator_submissions
  FOR INSERT WITH CHECK (true);
```

### Erro: "Supabase not configured"

- ✅ Verifique se editou `src/config/supabase.js` com suas credenciais
- ✅ Confirme que as credenciais estão corretas
- ✅ Verifique se executou `pnpm install`

### Erro: "Table doesn't exist"

- ✅ Execute o SQL de criação no painel do Supabase
- ✅ Verifique se a tabela aparece em "Table Editor"

### Dados não aparecem no banco

- ✅ Verifique se o botão "Enviar" está funcionando
- ✅ Abra o console do navegador para ver erros
- ✅ Confirme se está no modo debug (`?debug=true` na URL)

## 📈 Análise dos Dados

### Queries Úteis

```sql
-- Total de submissões
SELECT COUNT(*) FROM calculator_submissions;

-- Patrimônio médio
SELECT AVG(patrimonio) FROM calculator_submissions;

-- Submissões por dia
SELECT DATE(submitted_at), COUNT(*)
FROM calculator_submissions
GROUP BY DATE(submitted_at)
ORDER BY DATE(submitted_at) DESC;

-- Ativos mais escolhidos
SELECT jsonb_array_elements(ativos_escolhidos)->>'product' as produto,
       COUNT(*) as vezes_escolhido
FROM calculator_submissions
GROUP BY produto
ORDER BY vezes_escolhido DESC;
```

## 🔄 Próximos Passos

1. **Dashboard Analytics**: Criar visualizações dos dados coletados
2. **Exportação de Dados**: Implementar exportação em Excel/CSV
3. **Relatórios Automáticos**: Enviar relatórios por email
4. **Segmentação**: Analisar perfis de investidores
5. **A/B Testing**: Testar diferentes versões da calculadora
