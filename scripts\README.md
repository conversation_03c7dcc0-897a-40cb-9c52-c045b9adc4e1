# 🚀 Scripts de Deploy - App Calc Reino & DGM Canvas

Scripts automatizados para facilitar o deploy de ambos os projetos em diferentes plataformas de hospedagem.

## 📋 Scripts Disponíveis

### Windows (`.bat`)

```bash
./scripts/deploy.bat
```

### Unix/Linux/macOS (`.sh`)

```bash
chmod +x ./scripts/deploy.sh
./scripts/deploy.sh
```

### Node.js (`.js`)

```bash
node ./scripts/deploy.js [comando]
```

### NPM Scripts

```bash
npm run deploy:help     # Mostrar ajuda
npm run deploy:build    # Build local de ambos os projetos
npm run deploy:vercel   # Deploy DGM Canvas para Vercel
npm run deploy:render   # Instruções para deploy no Render
npm run deploy:config   # Criar arquivos de configuração
npm run deploy:check    # Verificar dependências e configurações
```

## 🔧 Pré-requisitos

1. **Node.js 18+** instalado
2. **pnpm** instalado (`npm install -g pnpm`)
3. **Git** instalado
4. **Arquivo .env** configurado na raiz do projeto

### Configuração do .env

Crie um arquivo `.env` na raiz do workspace com:

```env
# App-Calc-Reino Configuration
NODE_ENV=production
PORT=3000

# Supabase (obrigatório)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Typebot (opcional)
TYPEBOT_API_URL=https://your-typebot-instance.com
TYPEBOT_API_TOKEN=your_typebot_token

# DGM Canvas (opcional)
VITE_API_BASE_URL=https://your-app-calc-reino-domain.com
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key

# CORS
CORS_ORIGIN=https://your-dgm-canvas-domain.com
```

## 🎯 Opções de Deploy

### 1. Build Local

Prepara ambos os projetos para deploy:

```bash
node scripts/deploy.js build
```

### 2. Deploy para Vercel (DGM Canvas)

Build e deploy automático para Vercel:

```bash
node scripts/deploy.js vercel
```

### 3. Deploy para Render (App-Calc-Reino)

Build e instruções para deploy no Render:

```bash
node scripts/deploy.js render
```

### 4. Criar Arquivos de Configuração

Gera arquivos de configuração necessários:

```bash
node scripts/deploy.js config
```

Cria:

- `app-calc-reino/Dockerfile`
- `dgm-canvas/vercel.json`
- `dgm-canvas/netlify.toml`
- `.env.example`

### 5. Verificar Configurações

Testa dependências e conectividade:

```bash
node scripts/deploy.js check
```

## 📁 Estrutura de Arquivos Gerados

```
workspace/
├── .env.example                    # Template de variáveis de ambiente
├── app-calc-reino/
│   ├── Dockerfile                 # Container Docker
│   └── dist/                      # Build de produção
└── dgm-canvas/
    ├── vercel.json               # Configuração Vercel
    ├── netlify.toml              # Configuração Netlify
    └── dist/                     # Build de produção
```

## 🌐 Plataformas Suportadas

### 🆓 Gratuitas

- **Vercel** - Ideal para DGM Canvas (React/Vite)
- **Netlify** - Alternativa para DGM Canvas
- **Render** - Melhor para App-Calc-Reino (Node.js)
- **Railway** - Fullstack para ambos
- **Fly.io** - Deploy global

### 💰 Pagas

- **DigitalOcean App Platform**
- **Heroku**
- **AWS/GCP/Azure**

## 🔍 Troubleshooting

### Erro: "Node.js não encontrado"

```bash
# Instalar Node.js 18+
# Windows: https://nodejs.org/
# Ubuntu: sudo apt install nodejs npm
# macOS: brew install node
```

### Erro: "pnpm não encontrado"

```bash
npm install -g pnpm
```

### Erro: "Variáveis de ambiente não definidas"

1. Copie `.env.example` para `.env`
2. Configure suas credenciais do Supabase
3. Execute novamente o script

### Erro: "Build não foi criado"

1. Verifique se há erros no código
2. Execute `npm install` ou `pnpm install`
3. Teste localmente: `npm run dev`

### Erro: "Falha na conexão com Supabase"

1. Verifique as credenciais no `.env`
2. Teste no dashboard do Supabase
3. Verifique se o projeto está ativo

## 📊 Monitoramento Pós-Deploy

### Verificações Recomendadas

- [ ] Apps funcionando nas URLs de produção
- [ ] Banco de dados conectado
- [ ] APIs respondendo corretamente
- [ ] CORS configurado
- [ ] SSL/HTTPS ativo

### Ferramentas de Monitoramento

- **UptimeRobot** (gratuito) - Monitoramento de uptime
- **Sentry** (gratuito até 5K erros/mês) - Error tracking
- **Google Analytics** - Analytics de uso
- **Vercel Analytics** (se usando Vercel)

## 🆘 Suporte

### Logs dos Scripts

Os scripts salvam logs detalhados no console. Em caso de erro:

1. Copie a mensagem de erro completa
2. Verifique se seguiu todos os pré-requisitos
3. Consulte a documentação das plataformas:
   - [Vercel Docs](https://vercel.com/docs)
   - [Render Docs](https://render.com/docs)
   - [Supabase Docs](https://supabase.com/docs)

### Comandos de Debug

```bash
# Verificar versões
node --version
pnpm --version
git --version

# Testar builds localmente
cd app-calc-reino && pnpm run build
cd dgm-canvas && npm run build

# Verificar variáveis de ambiente
node -e "console.log(process.env.SUPABASE_URL)"
```

---

**💡 Dica**: Execute sempre `npm run deploy:check` antes de fazer deploy para identificar problemas antecipadamente.

**⚠️ Importante**: Mantenha suas credenciais seguras e nunca faça commit do arquivo `.env` para o repositório.
