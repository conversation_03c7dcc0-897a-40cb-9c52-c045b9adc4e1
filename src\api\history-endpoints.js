/**
 * History API Endpoints for DGM Canvas Integration
 * Provides historical data access from Supabase to DGM Canvas
 */

import { supabase, TABLE_NAME } from '../config/supabase.js';

/**
 * Get recent submissions from Supabase
 * @param {number} limit - Number of records to return (default: 10)
 * @param {string} orderBy - Field to order by (default: 'created_at')
 * @returns {Promise<Object>} API response with submissions data
 */
export async function getRecentSubmissions(limit = 10, orderBy = 'created_at') {
  try {
    console.log(`📊 [HistoryAPI] Fetching ${limit} recent submissions...`);

    const { data, error } = await supabase
      .from(TABLE_NAME)
      .select('*')
      .order(orderBy, { ascending: false })
      .limit(limit);

    if (error) {
      console.error('❌ [HistoryAPI] Supabase query error:', error);
      return {
        success: false,
        error: error.message,
        data: null,
      };
    }

    console.log(`✅ [HistoryAPI] Found ${data.length} submissions`);

    // Format data for DGM Canvas
    const formattedData = data.map(formatSubmissionForDGM);

    return {
      success: true,
      data: formattedData,
      total: data.length,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('❌ [HistoryAPI] Error fetching recent submissions:', error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
}

/**
 * Get submissions by date range
 * @param {string} startDate - Start date (ISO string)
 * @param {string} endDate - End date (ISO string)
 * @param {number} limit - Maximum records to return
 * @returns {Promise<Object>} API response with submissions data
 */
export async function getSubmissionsByDateRange(startDate, endDate, limit = 50) {
  try {
    console.log(`📅 [HistoryAPI] Fetching submissions from ${startDate} to ${endDate}`);

    let query = supabase
      .from(TABLE_NAME)
      .select('*')
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .order('created_at', { ascending: false })
      .limit(limit);

    const { data, error } = await query;

    if (error) {
      console.error('❌ [HistoryAPI] Date range query error:', error);
      return {
        success: false,
        error: error.message,
        data: null,
      };
    }

    console.log(`✅ [HistoryAPI] Found ${data.length} submissions in date range`);

    const formattedData = data.map(formatSubmissionForDGM);

    return {
      success: true,
      data: formattedData,
      total: data.length,
      dateRange: { startDate, endDate },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('❌ [HistoryAPI] Error fetching submissions by date:', error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
}

/**
 * Get submission statistics
 * @returns {Promise<Object>} API response with statistics
 */
export async function getSubmissionStats() {
  try {
    console.log('📈 [HistoryAPI] Calculating submission statistics...');

    // Get total count
    const { count: totalCount, error: countError } = await supabase
      .from(TABLE_NAME)
      .select('*', { count: 'exact', head: true });

    if (countError) {
      throw new Error(`Count query failed: ${countError.message}`);
    }

    // Get recent submissions for calculations
    const { data: recentData, error: dataError } = await supabase
      .from(TABLE_NAME)
      .select('patrimonio, total_alocado, percentual_alocado, created_at')
      .order('created_at', { ascending: false })
      .limit(100);

    if (dataError) {
      throw new Error(`Data query failed: ${dataError.message}`);
    }

    // Calculate statistics
    const patrimonios = recentData.map((item) => item.patrimonio).filter(Boolean);
    const alocacoes = recentData.map((item) => item.total_alocado).filter(Boolean);
    const percentuais = recentData.map((item) => item.percentual_alocado).filter(Boolean);

    const stats = {
      totalSubmissions: totalCount,
      averagePatrimonio:
        patrimonios.length > 0 ? patrimonios.reduce((a, b) => a + b, 0) / patrimonios.length : 0,
      averageAllocation:
        alocacoes.length > 0 ? alocacoes.reduce((a, b) => a + b, 0) / alocacoes.length : 0,
      averageAllocationPercentage:
        percentuais.length > 0 ? percentuais.reduce((a, b) => a + b, 0) / percentuais.length : 0,
      submissionsToday: recentData.filter((item) => {
        const today = new Date().toDateString();
        return new Date(item.created_at).toDateString() === today;
      }).length,
      submissionsThisWeek: recentData.filter((item) => {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return new Date(item.created_at) >= weekAgo;
      }).length,
    };

    console.log('✅ [HistoryAPI] Statistics calculated:', stats);

    return {
      success: true,
      data: stats,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('❌ [HistoryAPI] Error calculating statistics:', error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
}

/**
 * Search submissions by user info
 * @param {string} searchTerm - Name or email to search for
 * @param {number} limit - Maximum records to return
 * @returns {Promise<Object>} API response with matching submissions
 */
export async function searchSubmissions(searchTerm, limit = 20) {
  try {
    console.log(`🔍 [HistoryAPI] Searching submissions for: "${searchTerm}"`);

    const { data, error } = await supabase
      .from(TABLE_NAME)
      .select('*')
      .or(`nome.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('❌ [HistoryAPI] Search query error:', error);
      return {
        success: false,
        error: error.message,
        data: null,
      };
    }

    console.log(`✅ [HistoryAPI] Found ${data.length} matching submissions`);

    const formattedData = data.map(formatSubmissionForDGM);

    return {
      success: true,
      data: formattedData,
      total: data.length,
      searchTerm,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('❌ [HistoryAPI] Error searching submissions:', error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
}

/**
 * Get single submission by ID
 * @param {string} submissionId - Submission UUID
 * @returns {Promise<Object>} API response with submission data
 */
export async function getSubmissionById(submissionId) {
  try {
    console.log(`🎯 [HistoryAPI] Fetching submission: ${submissionId}`);

    const { data, error } = await supabase
      .from(TABLE_NAME)
      .select('*')
      .eq('id', submissionId)
      .single();

    if (error) {
      console.error('❌ [HistoryAPI] Single submission query error:', error);
      return {
        success: false,
        error: error.message,
        data: null,
      };
    }

    console.log('✅ [HistoryAPI] Submission found');

    const formattedData = formatSubmissionForDGM(data);

    return {
      success: true,
      data: formattedData,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('❌ [HistoryAPI] Error fetching submission by ID:', error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
}

/**
 * Format Supabase submission for DGM Canvas consumption
 * @param {Object} submission - Raw Supabase submission
 * @returns {Object} Formatted submission for DGM Canvas
 */
function formatSubmissionForDGM(submission) {
  try {
    // Calculate totals
    const totalAlocado = submission.total_alocado || 0;
    const patrimonioRestante = (submission.patrimonio || 0) - totalAlocado;
    const percentualAlocado = submission.percentual_alocado || 0;

    return {
      // Identification
      id: submission.id,
      timestamp: submission.created_at,
      submittedAt: submission.submitted_at,

      // Financial data
      patrimonio: {
        total: submission.patrimonio || 0,
        totalFormatted: new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(submission.patrimonio || 0),
        alocado: totalAlocado,
        alocadoFormatted: new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(totalAlocado),
        restante: patrimonioRestante,
        restanteFormatted: new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(patrimonioRestante),
        percentualAlocado: Math.round(percentualAlocado * 100) / 100,
        percentualAlocadoFormatted: `${Math.round(percentualAlocado * 100) / 100}%`,
      },

      // Assets data
      ativos: {
        escolhidos: submission.ativos_escolhidos || [],
        alocacao: formatAllocationFromSupabase(submission.alocacao || {}),
      },

      // User information
      usuario: {
        nome: submission.nome || null,
        email: submission.email || null,
        hasUserData: !!(submission.nome || submission.email),
      },

      // Metadata
      metadata: {
        source: 'supabase-history',
        sessionId: submission.session_id,
        userAgent: submission.user_agent,
        typebotSessionId: submission.typebot_session_id,
        typebotResultId: submission.typebot_result_id,
        salesforceId: submission.salesforce_id,
        syncStatus: submission.sync_status,
      },
    };
  } catch (error) {
    console.error('❌ [HistoryAPI] Error formatting submission:', error);
    return {
      id: submission.id,
      error: 'Format error',
      raw: submission,
    };
  }
}

/**
 * Format allocation data from Supabase format to DGM Canvas format
 * @param {Object} allocation - Raw allocation from Supabase
 * @returns {Array} Formatted allocation array
 */
function formatAllocationFromSupabase(allocation) {
  if (!allocation || typeof allocation !== 'object') return [];

  return Object.entries(allocation).map(([key, data]) => ({
    key,
    category: data.category || 'unknown',
    product: data.product || 'unknown',
    value: data.value || 0,
    valueFormatted: new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(data.value || 0),
    percentage: data.percentage || 0,
    percentageFormatted: `${Math.round((data.percentage || 0) * 100) / 100}%`,
  }));
}

// Export all functions
export { formatAllocationFromSupabase, formatSubmissionForDGM };
