/**
 * WelcomeScreen Component
 * Shows initial instructions when no data is available
 */

export const WelcomeScreen = ({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <div style={{
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      background: 'rgba(255,255,255,0.9)',
      padding: '20px',
      borderRadius: '10px',
      textAlign: 'center',
      boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
    }}>
      <h2>🎯 DGM Canvas - Reino Calculator</h2>
      <p>Aguardando dados do app-calc-reino...</p>
      <p style={{ fontSize: '12px', color: '#666' }}>
        📡 Endpoint: <code>http://localhost:5173/api/data</code><br/>
        ✅ Servidor rodando na porta 5173<br/>
        🎨 Gráfico de torta interativo DGM.js
      </p>
    </div>
  );
};
