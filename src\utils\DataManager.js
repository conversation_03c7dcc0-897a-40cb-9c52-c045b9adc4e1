export class DataManager {
  constructor(config = {}) {
    this.config = {
      pollInterval: config.pollInterval || 2000,
      endpoint: config.endpoint || "/api/latest",
      historyEndpoint: config.historyEndpoint || this.getHistoryEndpoint(),
      ...config,
    };

    this.lastUpdate = null;
    this.currentData = null;
    this.historicalData = [];
    this.listeners = {};
    this.isPolling = false;
    this.pollTimer = null;
  }

  /**
   * Get default history endpoint based on environment
   * @returns {string} History API base URL
   */
  getHistoryEndpoint() {
    // Check if we're in development
    const isDevelopment =
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1";

    if (isDevelopment) {
      // Assume app-calc-reino history API runs on port 3001
      return "http://localhost:3001/api/history";
    }

    // Production - same origin as the main app
    return `${window.location.origin}/api/history`;
  }

  startPolling() {
    if (this.isPolling) return;

    this.isPolling = true;
    this.checkForData();

    this.pollTimer = setInterval(() => {
      this.checkForData();
    }, this.config.pollInterval);
  }

  stopPolling() {
    if (!this.isPolling) return;

    this.isPolling = false;

    if (this.pollTimer) {
      clearInterval(this.pollTimer);
      this.pollTimer = null;
    }
  }

  log(level, message, data = null) {
    if (level === "error") {
      console.error(`❌ [DataManager] ${message}`, data);
    } else if (level === "warn") {
      console.warn(`⚠️ [DataManager] ${message}`, data);
    }

    if (level === "error" || level === "warn") {
      try {
        fetch("/api/debug-log", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            level,
            message: `[DataManager] ${message}`,
            data,
            timestamp: new Date().toISOString(),
          }),
        }).catch(() => {});
      } catch (e) {}
    }
  }

  async checkForData() {
    try {
      const response = await fetch(this.config.endpoint);
      const result = await response.json();

      if (result.data && result.receivedAt !== this.lastUpdate) {
        this.lastUpdate = result.receivedAt;
        this.currentData = result.data;

        const isValid = this.validateData(result.data);

        if (isValid) {
          const processedData = this.preprocessData(result.data);

          this.notifyListeners("dataReceived", {
            data: processedData,
            receivedAt: result.receivedAt,
          });
        } else {
          this.log("warn", "Invalid data format received");
        }
      } else if (result.data && !this.currentData) {
        this.lastUpdate = result.receivedAt;
        this.currentData = result.data;

        const isValid = this.validateData(result.data);

        if (isValid) {
          const processedData = this.preprocessData(result.data);

          this.notifyListeners("dataReceived", {
            data: processedData,
            receivedAt: result.receivedAt,
          });
        } else {
          this.log("warn", "Invalid data format on first load");
        }
      }
      // Removed the "No new data available" log to reduce console spam
    } catch (error) {
      console.error("❌ [DataManager] Error fetching data:", error);
      this.log("error", "Error fetching data", error);
      this.notifyListeners("error", { error });
    }
  }

  preprocessData(data) {
    const processedData = { ...data };

    if (processedData.ativos && processedData.ativos.alocacao) {
      processedData.ativos.alocacao = processedData.ativos.alocacao.filter(
        (item) => {
          const itemValue = item.value || item.valorAlocado;
          return itemValue && itemValue > 0;
        }
      );
    }

    return processedData;
  }

  validateData(data) {
    if (!data || typeof data !== "object") return false;

    const requiredFields = ["patrimonio", "ativos", "usuario"];
    for (const field of requiredFields) {
      if (!data[field]) {
        this.log("warn", `Missing required field: ${field}`);
        return false;
      }
    }

    if (!data.patrimonio.total || typeof data.patrimonio.total !== "number") {
      return false;
    }

    if (!data.ativos.alocacao || !Array.isArray(data.ativos.alocacao)) {
      return false;
    }

    for (const item of data.ativos.alocacao) {
      const productName = item.product || item.nome;
      if (!productName || typeof productName !== "string") {
        return false;
      }

      const itemValue =
        item.value !== undefined ? item.value : item.valorAlocado;
      if (typeof itemValue !== "number" || itemValue < 0) {
        return false;
      }

      if (!item.product && item.nome) {
        item.product = item.nome;
      }
      if (item.value === undefined && item.valorAlocado !== undefined) {
        item.value = item.valorAlocado;
      }

      if (!item.valueFormatted) {
        item.valueFormatted = new Intl.NumberFormat("pt-BR", {
          style: "currency",
          currency: "BRL",
        }).format(item.value);
      }

      if (!item.percentageFormatted) {
        item.percentageFormatted = `${Math.round(item.percentage || 0)}%`;
      }
    }

    if (!data.usuario || typeof data.usuario !== "object") {
      return false;
    }

    return true;
  }

  addEventListener(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  removeEventListener(event, callback) {
    if (!this.listeners[event]) return;

    const index = this.listeners[event].indexOf(callback);
    if (index > -1) {
      this.listeners[event].splice(index, 1);
    }
  }

  notifyListeners(event, data) {
    if (!this.listeners[event]) return;

    this.listeners[event].forEach((callback) => {
      try {
        callback(data);
      } catch (error) {
        console.error("❌ [DataManager] Error in event listener:", error);
      }
    });
  }

  getCurrentData() {
    return this.currentData;
  }

  getLastUpdate() {
    return this.lastUpdate;
  }

  getStatus() {
    return {
      isPolling: this.isPolling,
      hasData: !!this.currentData,
      lastUpdate: this.lastUpdate,
    };
  }

  static createTestData() {
    return {
      patrimonio: {
        total: 100000,
        alocado: 75000,
        percentualAlocado: 75,
        restante: 25000,
      },
      ativos: {
        alocacao: [
          {
            product: "Renda Fixa",
            value: 30000,
            valueFormatted: "R$ 30.000",
            percentageFormatted: "30%",
          },
          {
            product: "Ações",
            value: 25000,
            valueFormatted: "R$ 25.000",
            percentageFormatted: "25%",
          },
          {
            product: "FIIs",
            value: 15000,
            valueFormatted: "R$ 15.000",
            percentageFormatted: "15%",
          },
          {
            product: "Cripto",
            value: 5000,
            valueFormatted: "R$ 5.000",
            percentageFormatted: "5%",
          },
        ],
      },
      usuario: {
        nome: "Usuário Teste",
      },
      timestamp: new Date().toISOString(),
    };
  }

  loadTestData() {
    const testData = DataManager.createTestData();
    this.currentData = testData;
    this.lastUpdate = testData.timestamp;

    this.notifyListeners("dataReceived", {
      data: testData,
      receivedAt: testData.timestamp,
    });
  }

  cleanup() {
    this.stopPolling();
    this.listeners = {};
    this.currentData = null;
    this.lastUpdate = null;
    this.historicalData = [];
  }

  // === HISTORICAL DATA METHODS ===

  /**
   * Fetch recent submissions from history API
   * @param {number} limit - Number of records to fetch
   * @returns {Promise<Object>} API response with historical data
   */
  async fetchRecentSubmissions(limit = 10) {
    try {
      console.log(`📊 [DataManager] Fetching ${limit} recent submissions...`);

      const response = await fetch(
        `${this.config.historyEndpoint}/recent?limit=${limit}`
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        this.historicalData = result.data || [];

        this.notifyListeners("historicalDataReceived", {
          data: this.historicalData,
          total: result.total,
          type: "recent",
          timestamp: result.timestamp,
        });

        console.log(
          `✅ [DataManager] Loaded ${this.historicalData.length} historical records`
        );
      }

      return result;
    } catch (error) {
      this.log("error", "Failed to fetch recent submissions", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fetch submission statistics
   * @returns {Promise<Object>} API response with statistics
   */
  async fetchStats() {
    try {
      console.log("📈 [DataManager] Fetching submission statistics...");

      const response = await fetch(`${this.config.historyEndpoint}/stats`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        this.notifyListeners("statsReceived", {
          stats: result.data,
          timestamp: result.timestamp,
        });

        console.log("✅ [DataManager] Statistics loaded:", result.data);
      }

      return result;
    } catch (error) {
      this.log("error", "Failed to fetch statistics", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Search submissions by user info
   * @param {string} searchTerm - Name or email to search for
   * @param {number} limit - Maximum records to return
   * @returns {Promise<Object>} API response with search results
   */
  async searchSubmissions(searchTerm, limit = 20) {
    try {
      console.log(`🔍 [DataManager] Searching for: "${searchTerm}"`);

      const response = await fetch(
        `${this.config.historyEndpoint}/search?q=${encodeURIComponent(
          searchTerm
        )}&limit=${limit}`
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        this.notifyListeners("searchResultsReceived", {
          data: result.data || [],
          total: result.total,
          searchTerm: result.searchTerm,
          timestamp: result.timestamp,
        });

        console.log(
          `✅ [DataManager] Found ${result.data?.length || 0} matching records`
        );
      }

      return result;
    } catch (error) {
      this.log("error", "Failed to search submissions", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fetch submissions by date range
   * @param {string} startDate - Start date (ISO string)
   * @param {string} endDate - End date (ISO string)
   * @param {number} limit - Maximum records to return
   * @returns {Promise<Object>} API response with date-filtered data
   */
  async fetchByDateRange(startDate, endDate, limit = 50) {
    try {
      console.log(
        `📅 [DataManager] Fetching submissions from ${startDate} to ${endDate}`
      );

      const response = await fetch(
        `${this.config.historyEndpoint}/date-range?startDate=${startDate}&endDate=${endDate}&limit=${limit}`
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        this.notifyListeners("dateRangeDataReceived", {
          data: result.data || [],
          total: result.total,
          dateRange: result.dateRange,
          timestamp: result.timestamp,
        });

        console.log(
          `✅ [DataManager] Found ${
            result.data?.length || 0
          } records in date range`
        );
      }

      return result;
    } catch (error) {
      this.log("error", "Failed to fetch date range data", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Test connection to history API
   * @returns {Promise<Object>} Connection test result
   */
  async testHistoryConnection() {
    try {
      console.log("🔗 [DataManager] Testing history API connection...");

      const response = await fetch(this.config.historyEndpoint);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      console.log(
        "✅ [DataManager] History API connection successful:",
        result.version
      );

      return { success: true, data: result };
    } catch (error) {
      console.warn(
        "⚠️ [DataManager] History API connection failed:",
        error.message
      );
      return { success: false, error: error.message };
    }
  }

  /**
   * Get cached historical data
   * @returns {Array} Array of historical submissions
   */
  getHistoricalData() {
    return this.historicalData;
  }

  /**
   * Load historical data and switch to historical view mode
   * @param {number} limit - Number of recent records to load
   */
  async loadHistoricalView(limit = 10) {
    try {
      const result = await this.fetchRecentSubmissions(limit);

      if (result.success && this.historicalData.length > 0) {
        // Use the most recent historical record as current data
        const mostRecent = this.historicalData[0];
        this.currentData = mostRecent;
        this.lastUpdate = mostRecent.timestamp;

        this.notifyListeners("dataReceived", {
          data: mostRecent,
          receivedAt: mostRecent.timestamp,
          isHistorical: true,
        });

        console.log("🔄 [DataManager] Switched to historical view mode");
        return true;
      }

      return false;
    } catch (error) {
      this.log("error", "Failed to load historical view", error);
      return false;
    }
  }

  /**
   * Switch to a specific historical record
   * @param {number} index - Index in the historical data array
   */
  switchToHistoricalRecord(index) {
    if (index >= 0 && index < this.historicalData.length) {
      const record = this.historicalData[index];
      this.currentData = record;
      this.lastUpdate = record.timestamp;

      this.notifyListeners("dataReceived", {
        data: record,
        receivedAt: record.timestamp,
        isHistorical: true,
        recordIndex: index,
      });

      console.log(
        `🔄 [DataManager] Switched to historical record ${index + 1}/${
          this.historicalData.length
        }`
      );
      return true;
    }

    console.warn(`⚠️ [DataManager] Invalid historical record index: ${index}`);
    return false;
  }
}
