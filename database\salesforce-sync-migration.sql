-- Migration: Add Salesforce sync fields to calculator_submissions table
-- This migration adds fields needed for Salesforce integration and synchronization

-- Add Salesforce sync fields to existing table
ALTER TABLE public.calculator_submissions 
ADD COLUMN IF NOT EXISTS salesforce_id TEXT,
ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS synced_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS sync_error TEXT,
ADD COLUMN IF NOT EXISTS last_sync_attempt TIMESTAMPTZ;

-- Create index for Salesforce sync queries
CREATE INDEX IF NOT EXISTS idx_calculator_submissions_sync_status 
ON public.calculator_submissions(sync_status);

CREATE INDEX IF NOT EXISTS idx_calculator_submissions_salesforce_id 
ON public.calculator_submissions(salesforce_id);

-- Add constraint to ensure unique Salesforce IDs (when present)
ALTER TABLE public.calculator_submissions 
ADD CONSTRAINT unique_salesforce_id 
UNIQUE (salesforce_id);

-- Create enum for sync status (optional - improves data integrity)
DO $$ BEGIN
    CREATE TYPE sync_status_enum AS ENUM ('pending', 'synced', 'failed', 'manual');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update sync_status column to use enum (optional)
-- Note: This will fail if there are existing rows with invalid status values
-- ALTER TABLE public.calculator_submissions 
-- ALTER COLUMN sync_status TYPE sync_status_enum USING sync_status::sync_status_enum;

-- Create function to auto-set sync status on insert
CREATE OR REPLACE FUNCTION set_default_sync_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Set default sync status for new records
    IF NEW.sync_status IS NULL THEN
        NEW.sync_status := 'pending';
    END IF;
    
    -- Set last_sync_attempt timestamp when sync_status changes
    IF TG_OP = 'UPDATE' AND OLD.sync_status IS DISTINCT FROM NEW.sync_status THEN
        NEW.last_sync_attempt := NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically set sync status
DROP TRIGGER IF EXISTS trigger_set_sync_status ON public.calculator_submissions;
CREATE TRIGGER trigger_set_sync_status
    BEFORE INSERT OR UPDATE ON public.calculator_submissions
    FOR EACH ROW
    EXECUTE FUNCTION set_default_sync_status();

-- Create view for sync monitoring (useful for debugging)
CREATE OR REPLACE VIEW public.sync_status_summary AS
SELECT 
    sync_status,
    COUNT(*) as count,
    MIN(created_at) as oldest_record,
    MAX(created_at) as newest_record,
    COUNT(CASE WHEN synced_at IS NOT NULL THEN 1 END) as synced_count,
    COUNT(CASE WHEN sync_error IS NOT NULL THEN 1 END) as error_count
FROM public.calculator_submissions
GROUP BY sync_status
ORDER BY sync_status;

-- Grant necessary permissions for the view
GRANT SELECT ON public.sync_status_summary TO anon;
GRANT SELECT ON public.sync_status_summary TO authenticated;

-- Add comments for documentation
COMMENT ON COLUMN public.calculator_submissions.salesforce_id IS 'Salesforce record ID after successful sync';
COMMENT ON COLUMN public.calculator_submissions.sync_status IS 'Current synchronization status: pending, synced, failed, manual';
COMMENT ON COLUMN public.calculator_submissions.synced_at IS 'Timestamp when record was successfully synced to Salesforce';
COMMENT ON COLUMN public.calculator_submissions.sync_error IS 'Error message if sync failed';
COMMENT ON COLUMN public.calculator_submissions.last_sync_attempt IS 'Timestamp of last sync attempt (success or failure)';

-- Example query to check sync status
-- SELECT sync_status, COUNT(*) FROM public.calculator_submissions GROUP BY sync_status;

-- Example query to find failed syncs
-- SELECT id, sync_error, last_sync_attempt FROM public.calculator_submissions WHERE sync_status = 'failed';

-- Example query to retry failed syncs (update status to pending)
-- UPDATE public.calculator_submissions SET sync_status = 'pending', sync_error = NULL WHERE sync_status = 'failed';
