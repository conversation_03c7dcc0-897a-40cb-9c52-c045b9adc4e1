import React, { useState, useEffect } from 'react';

/**
 * Historical Data Control Panel Component
 * Provides UI controls for accessing and navigating historical data
 */
export function HistoryControlPanel({ dataManager }) {
  const [isHistoryMode, setIsHistoryMode] = useState(false);
  const [historicalData, setHistoricalData] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [stats, setStats] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState(null);

  // Test connection on mount
  useEffect(() => {
    if (dataManager) {
      testConnection();
    }
  }, [dataManager]);

  // Set up event listeners
  useEffect(() => {
    if (!dataManager) return;

    const handleHistoricalData = ({ data, total, type }) => {
      setHistoricalData(data);
      setCurrentIndex(0);
      setIsHistoryMode(true);
      setIsLoading(false);
      console.log(`📊 [HistoryControl] Received ${total} ${type} records`);
    };

    const handleStats = ({ stats }) => {
      setStats(stats);
      console.log('📈 [HistoryControl] Stats updated:', stats);
    };

    const handleSearchResults = ({ data, total, searchTerm: term }) => {
      setHistoricalData(data);
      setCurrentIndex(0);
      setIsHistoryMode(true);
      setIsLoading(false);
      console.log(`🔍 [HistoryControl] Search "${term}": ${total} results`);
    };

    dataManager.addEventListener('historicalDataReceived', handleHistoricalData);
    dataManager.addEventListener('statsReceived', handleStats);
    dataManager.addEventListener('searchResultsReceived', handleSearchResults);
    dataManager.addEventListener('dateRangeDataReceived', handleHistoricalData);

    return () => {
      dataManager.removeEventListener('historicalDataReceived', handleHistoricalData);
      dataManager.removeEventListener('statsReceived', handleStats);
      dataManager.removeEventListener('searchResultsReceived', handleSearchResults);
      dataManager.removeEventListener('dateRangeDataReceived', handleHistoricalData);
    };
  }, [dataManager]);

  const testConnection = async () => {
    if (!dataManager) return;
    
    const result = await dataManager.testHistoryConnection();
    setConnectionStatus(result.success ? 'connected' : 'failed');
  };

  const loadRecentData = async (limit = 10) => {
    if (!dataManager) return;
    
    setIsLoading(true);
    await dataManager.fetchRecentSubmissions(limit);
  };

  const loadStats = async () => {
    if (!dataManager) return;
    
    setIsLoading(true);
    await dataManager.fetchStats();
    setIsLoading(false);
  };

  const searchData = async () => {
    if (!dataManager || !searchTerm.trim()) return;
    
    setIsLoading(true);
    await dataManager.searchSubmissions(searchTerm.trim());
  };

  const goToRecord = (index) => {
    if (dataManager && historicalData.length > 0) {
      setCurrentIndex(index);
      dataManager.switchToHistoricalRecord(index);
    }
  };

  const exitHistoryMode = () => {
    setIsHistoryMode(false);
    setHistoricalData([]);
    setCurrentIndex(0);
    // Resume live polling
    if (dataManager) {
      dataManager.startPolling();
    }
  };

  const currentRecord = historicalData[currentIndex];

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '15px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      maxWidth: '300px',
      fontSize: '14px',
      zIndex: 1000,
    }}>
      <h4 style={{ margin: '0 0 15px 0', color: '#333' }}>
        📊 Controle de Histórico
      </h4>

      {/* Connection Status */}
      <div style={{ marginBottom: '15px' }}>
        <strong>Status da API:</strong>
        <span style={{ 
          marginLeft: '8px',
          color: connectionStatus === 'connected' ? 'green' : 
                 connectionStatus === 'failed' ? 'red' : 'orange'
        }}>
          {connectionStatus === 'connected' ? '✅ Conectado' :
           connectionStatus === 'failed' ? '❌ Desconectado' : '🔄 Testando...'}
        </span>
      </div>

      {/* Quick Actions */}
      <div style={{ marginBottom: '15px' }}>
        <button 
          onClick={() => loadRecentData(5)}
          disabled={isLoading}
          style={{ 
            marginRight: '8px',
            padding: '5px 10px',
            fontSize: '12px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            background: isLoading ? '#f5f5f5' : '#fff',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? '⏳' : '📊'} Últimos 5
        </button>

        <button 
          onClick={() => loadRecentData(10)}
          disabled={isLoading}
          style={{ 
            marginRight: '8px',
            padding: '5px 10px',
            fontSize: '12px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            background: isLoading ? '#f5f5f5' : '#fff',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? '⏳' : '📊'} Últimos 10
        </button>

        <button 
          onClick={loadStats}
          disabled={isLoading}
          style={{ 
            padding: '5px 10px',
            fontSize: '12px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            background: isLoading ? '#f5f5f5' : '#fff',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? '⏳' : '📈'} Stats
        </button>
      </div>

      {/* Search */}
      <div style={{ marginBottom: '15px' }}>
        <input
          type="text"
          placeholder="Buscar por nome ou email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && searchData()}
          style={{
            width: '100%',
            padding: '5px',
            fontSize: '12px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            marginBottom: '5px'
          }}
        />
        <button 
          onClick={searchData}
          disabled={isLoading || !searchTerm.trim()}
          style={{ 
            width: '100%',
            padding: '5px',
            fontSize: '12px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            background: isLoading || !searchTerm.trim() ? '#f5f5f5' : '#fff',
            cursor: isLoading || !searchTerm.trim() ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? '⏳ Buscando...' : '🔍 Buscar'}
        </button>
      </div>

      {/* History Navigation */}
      {isHistoryMode && historicalData.length > 0 && (
        <div style={{ marginBottom: '15px', padding: '10px', background: '#f8f9fa', borderRadius: '4px' }}>
          <div style={{ marginBottom: '8px' }}>
            <strong>📋 Modo Histórico</strong>
            <button 
              onClick={exitHistoryMode}
              style={{ 
                float: 'right',
                padding: '2px 6px',
                fontSize: '10px',
                border: '1px solid #ddd',
                borderRadius: '3px',
                background: '#fff',
                cursor: 'pointer'
              }}
            >
              ❌ Sair
            </button>
          </div>
          
          <div style={{ marginBottom: '8px' }}>
            Registro: {currentIndex + 1} / {historicalData.length}
          </div>
          
          <div style={{ marginBottom: '8px' }}>
            <button 
              onClick={() => goToRecord(Math.max(0, currentIndex - 1))}
              disabled={currentIndex === 0}
              style={{ 
                marginRight: '5px',
                padding: '3px 8px',
                fontSize: '11px',
                border: '1px solid #ddd',
                borderRadius: '3px',
                background: currentIndex === 0 ? '#f5f5f5' : '#fff',
                cursor: currentIndex === 0 ? 'not-allowed' : 'pointer'
              }}
            >
              ⬅️ Anterior
            </button>
            
            <button 
              onClick={() => goToRecord(Math.min(historicalData.length - 1, currentIndex + 1))}
              disabled={currentIndex === historicalData.length - 1}
              style={{ 
                padding: '3px 8px',
                fontSize: '11px',
                border: '1px solid #ddd',
                borderRadius: '3px',
                background: currentIndex === historicalData.length - 1 ? '#f5f5f5' : '#fff',
                cursor: currentIndex === historicalData.length - 1 ? 'not-allowed' : 'pointer'
              }}
            >
              Próximo ➡️
            </button>
          </div>
          
          {currentRecord && (
            <div style={{ fontSize: '11px', color: '#666' }}>
              <div><strong>👤</strong> {currentRecord.usuario.nome || 'N/A'}</div>
              <div><strong>💰</strong> {currentRecord.patrimonio.totalFormatted}</div>
              <div><strong>📅</strong> {new Date(currentRecord.timestamp).toLocaleString('pt-BR')}</div>
            </div>
          )}
        </div>
      )}

      {/* Statistics Display */}
      {stats && (
        <div style={{ marginBottom: '15px', padding: '10px', background: '#e8f5e8', borderRadius: '4px' }}>
          <div style={{ marginBottom: '5px' }}><strong>📈 Estatísticas</strong></div>
          <div style={{ fontSize: '11px' }}>
            <div>📊 Total: {stats.totalSubmissions}</div>
            <div>💰 Patrimônio médio: {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(stats.averagePatrimonio)}</div>
            <div>📅 Hoje: {stats.submissionsToday}</div>
            <div>📅 Esta semana: {stats.submissionsThisWeek}</div>
          </div>
        </div>
      )}

      {/* Status */}
      <div style={{ fontSize: '11px', color: '#666' }}>
        {isLoading && '⏳ Carregando...'}
        {!isLoading && !isHistoryMode && '🔄 Modo ao vivo'}
        {!isLoading && isHistoryMode && `📋 ${historicalData.length} registros carregados`}
      </div>
    </div>
  );
}
