import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@dgmjs/react";
import { useState, useEffect, useRef } from "react";
import { DataManager } from "./utils/DataManager.js";
import { useDGMCanvas } from "./utils/useDGMCanvas.js";
import { StatusPanel } from "./components/StatusPanel.jsx";
import { WelcomeScreen } from "./components/WelcomeScreen.jsx";
import { ChartControls } from "./components/ChartControls.jsx";
import { HistoryControlPanel } from "./components/HistoryControlPanel.jsx";

function App() {
  const [receivedData, setReceivedData] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);
  const dataManagerRef = useRef(null);

  // Initialize DataManager
  useEffect(() => {
    const dataManager = new DataManager({
      pollInterval: 2000,
      endpoint: '/api/latest'
    });

    dataManagerRef.current = dataManager;

    // Set up event listeners
    dataManager.addEventListener('dataReceived', ({ data, receivedAt }) => {
      setReceivedData(data);
      setLastUpdate(receivedAt);
      
      // Trigger automatic chart creation when data is received
      setTimeout(() => {
        if (pieChart && data) {
          updateCanvasWithData(data);
        }
      }, 100); // Small delay to ensure all components are ready
    });

    dataManager.addEventListener('error', ({ error }) => {
      console.error('❌ [App] Data manager error:', error);
    });

    // Start polling
    dataManager.startPolling();

    // Cleanup on unmount
    return () => {
      dataManager.cleanup();
    };
  }, []);

  // Use DGM Canvas hook
  const { handleMount, updateCanvasWithData, pieChart, d3PieChart, editor } = useDGMCanvas(dataManagerRef.current);

  // Auto-create chart when data is received and components are ready
  useEffect(() => {
    if (receivedData && pieChart && updateCanvasWithData) {
      updateCanvasWithData(receivedData);
    }
  }, [receivedData, pieChart, updateCanvasWithData]);

  // Backup mechanism - check periodically if data exists but chart wasn't created
  useEffect(() => {
    if (!receivedData || !pieChart) return;

    const checkAndCreateChart = () => {
      if (receivedData && pieChart && updateCanvasWithData) {
        // Check if canvas is empty (no chart created yet)
        if (editor && editor.doc && editor.doc.children && editor.doc.children.length === 0) {
          updateCanvasWithData(receivedData);
        }
      }
    };

    const backupTimer = setInterval(checkAndCreateChart, 3000); // Check every 3 seconds

    return () => clearInterval(backupTimer);
  }, [receivedData, pieChart, updateCanvasWithData, editor]);

  // Test function for manual chart creation
  const testPieChart = () => {
    console.log("🧪 [App] Manual test triggered");
    console.log("🧪 [App] DataManager:", !!dataManagerRef.current);
    console.log("🧪 [App] PieChart:", !!pieChart);
    console.log("🧪 [App] Current data:", dataManagerRef.current?.currentData);
    
    if (pieChart && dataManagerRef.current?.currentData) {
      const data = dataManagerRef.current.currentData;
      console.log("🧪 [App] Creating chart manually with data:", data);
      updateCanvasWithData(data);
    } else {
      console.log("❌ [App] Missing dependencies for manual test:", {
        hasPieChart: !!pieChart,
        hasDataManager: !!dataManagerRef.current,
        hasCurrentData: !!dataManagerRef.current?.currentData
      });
    }
  };

  // Handle data changes from DataControls
  const handleDataChange = (newData) => {
    console.log("📊 [App] Data changed:", newData);
    
    // Update the current data in DataManager
    if (dataManagerRef.current) {
      dataManagerRef.current.currentData = newData;
    }
    
    // Update local state
    setReceivedData(newData);
    
    // Recreate chart with new data
    if (pieChart) {
      updateCanvasWithData(newData);
    }
  };

  return (
    <div style={{ width: '100vw', height: '100vh', position: 'relative' }}>
      {/* Status Panel */}
      <StatusPanel 
        dataManager={dataManagerRef.current}
        receivedData={receivedData}
        lastUpdate={lastUpdate}
      />

      {/* Chart Controls */}
      <ChartControls 
        pieChart={pieChart}
        d3PieChart={d3PieChart}
        currentData={receivedData}
        onDataChange={handleDataChange}
        editor={editor}
        isVisible={!!receivedData}
      />

      {/* History Control Panel */}
      <HistoryControlPanel dataManager={dataManagerRef.current} />

      {/* Test Button - Temporary for debugging */}
      {receivedData && (
        <button 
          onClick={testPieChart}
          style={{
            position: 'absolute',
            top: '10px',
            right: '330px', // Moved to avoid overlap with history panel
            zIndex: 1000,
            padding: '10px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          🧪 Criar Gráfico Manualmente
        </button>
      )}

      {/* Canvas */}
      <DGMEditor
        style={{ width: '100%', height: '100%' }}
        onMount={handleMount}
      />
      
      {/* Welcome Screen */}
      <WelcomeScreen isVisible={!receivedData} />
    </div>
  );
}

export default App;
