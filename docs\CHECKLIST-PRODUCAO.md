# ✅ Checklist de Produção - Sistema de Histórico

## 📋 Pré-Deploy

### Backend (app-calc-reino)

- [ ] Dependências instaladas (`express`, `cors`)
- [ ] Arquivo `history-server-prod.js` criado
- [ ] Variáveis de ambiente configuradas no `.env`
- [ ] Conexão com Supabase testada
- [ ] Arquivo `ecosystem.config.js` criado para PM2

### Frontend (DGM Canvas)

- [ ] Arquivo `src/config/production.js` configurado
- [ ] URLs de produção atualizadas
- [ ] Build de produção testado (`npm run build`)
- [ ] Arquivos de dist/ gerados corretamente

### Infraestrutura

- [ ] Servidor web configurado (Nginx/Apache)
- [ ] SSL/TLS configurado (se necessário)
- [ ] Domínios apontando para o servidor
- [ ] PM2 instalado globalmente
- [ ] Portas 3000 e 3001 liberadas no firewall

## 🚀 Deploy

### 1. Deploy do Backend

```bash
cd "c:\Users\<USER>\Desktop\Integração Reino\app-calc-reino"

# Instalar dependências de produção
npm install express cors winston express-rate-limit

# Iniciar com PM2
pm2 start ecosystem.config.js

# Verificar status
pm2 status
pm2 logs history-api
```

### 2. Deploy do Frontend

```bash
cd "c:\Users\<USER>\Desktop\Zero\dgm-canvas"

# Build para produção
npm run build

# Copiar arquivos para servidor web
# (exemplo para nginx)
sudo cp -r dist/* /var/www/dgm-canvas/

# Ou usar o script automatizado
node scripts/deploy.js deploy
```

### 3. Configuração do Servidor Web

#### Nginx

```bash
# Copiar configuração
sudo cp nginx.conf /etc/nginx/sites-available/dgm-canvas
sudo ln -s /etc/nginx/sites-available/dgm-canvas /etc/nginx/sites-enabled/

# Testar configuração
sudo nginx -t

# Reiniciar nginx
sudo systemctl restart nginx
```

## 🧪 Testes Pós-Deploy

### Testes Básicos

- [ ] Health check: `curl https://seu-dominio.com/api/history/health`
- [ ] API recente: `curl https://seu-dominio.com/api/history/recent?limit=3`
- [ ] API stats: `curl https://seu-dominio.com/api/history/stats`
- [ ] Frontend carrega: Abrir `https://dgm-canvas.seu-dominio.com`

### Testes de Integração

- [ ] DGM Canvas conecta com API de histórico
- [ ] Painel de histórico aparece na interface
- [ ] Dados históricos são carregados corretamente
- [ ] Busca de histórico funciona
- [ ] Navegação entre registros funciona

### Testes de Performance

- [ ] Tempo de resposta da API < 500ms
- [ ] Frontend carrega em < 3 segundos
- [ ] Sem erros no console do navegador
- [ ] Sem vazamentos de memória

## 📊 Monitoramento

### Logs

```bash
# Logs da API de histórico
pm2 logs history-api

# Logs do nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Logs do sistema
journalctl -u nginx -f
```

### Métricas

- [ ] CPU < 70%
- [ ] Memória < 80%
- [ ] Espaço em disco > 20% livre
- [ ] Conexões de banco de dados estáveis

## 🔧 Troubleshooting

### Problemas Comuns

#### 1. API não responde

```bash
# Verificar se o processo está rodando
pm2 status

# Reiniciar se necessário
pm2 restart history-api

# Verificar logs
pm2 logs history-api --lines 50
```

#### 2. CORS errors

```bash
# Verificar configuração no history-server-prod.js
# Verificar se os domínios estão corretos
```

#### 3. Banco de dados não conecta

```bash
# Verificar variáveis de ambiente
echo $SUPABASE_URL
echo $SUPABASE_ANON_KEY

# Testar conexão manual
```

#### 4. Frontend não carrega

```bash
# Verificar nginx
sudo nginx -t
sudo systemctl status nginx

# Verificar arquivos estáticos
ls -la /var/www/dgm-canvas/
```

## 🛡️ Segurança

### Checklist de Segurança

- [ ] Rate limiting ativo
- [ ] CORS configurado corretamente
- [ ] Headers de segurança configurados
- [ ] SSL/TLS ativo (HTTPS)
- [ ] Firewall configurado
- [ ] Logs de segurança ativos

### Headers de Segurança (Nginx)

```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

## 📈 Performance

### Otimizações Implementadas

- [ ] Compressão gzip ativa
- [ ] Cache de arquivos estáticos
- [ ] Minificação de CSS/JS
- [ ] Imagens otimizadas
- [ ] API rate limiting

### Configuração de Cache

```nginx
# Cache de arquivos estáticos
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 🔄 Manutenção

### Tarefas Regulares

#### Diárias

- [ ] Verificar logs de erro
- [ ] Verificar status dos serviços
- [ ] Verificar uso de recursos

#### Semanais

- [ ] Verificar atualizações de segurança
- [ ] Revisar métricas de performance
- [ ] Backup da configuração

#### Mensais

- [ ] Limpar logs antigos
- [ ] Atualizar dependências
- [ ] Revisar políticas de segurança

### Scripts de Manutenção

#### Limpeza de Logs

```bash
#!/bin/bash
# Manter apenas últimos 30 dias de logs
find /var/log -name "*.log" -mtime +30 -delete
pm2 flush
```

#### Backup de Configuração

```bash
#!/bin/bash
# Backup de arquivos de configuração
tar -czf backup-config-$(date +%Y%m%d).tar.gz \
  /etc/nginx/sites-available/dgm-canvas \
  ecosystem.config.js \
  .env
```

## 📞 Contatos de Emergência

### Em caso de problemas críticos

1. **Verificar status geral:**

   ```bash
   pm2 status
   sudo systemctl status nginx
   ```

2. **Restart completo:**

   ```bash
   pm2 restart all
   sudo systemctl restart nginx
   ```

3. **Rollback (se necessário):**

   ```bash
   # Voltar para backup anterior
   sudo cp -r /backup/last-known-good/* /var/www/dgm-canvas/
   pm2 restart history-api
   ```

## ✅ Finalização

### Quando tudo estiver funcionando

- [ ] Documentar configurações específicas
- [ ] Compartilhar URLs de acesso com equipe
- [ ] Configurar monitoramento automatizado
- [ ] Agendar primeira revisão de performance
- [ ] Celebrar o deploy! 🎉

---

**📅 Data do Deploy:** _______________  
**👤 Responsável:** _______________  
**🔗 URLs de Produção:**

- Frontend: <https://dgm-canvas.seu-dominio.com>
- API: <https://api.seu-dominio.com/api/history>
- Health: <https://api.seu-dominio.com/api/history/health>

**✅ Status:** [ ] Em progresso [ ] Concluído [ ] Com problemas
