# Scroll Float Animation Setup Guide

## Overview

This guide explains how to setup the scroll-triggered animation for the `componente-alocao-float` component. The animation triggers when the user scrolls to the middle of the `_3-section-patrimonio-alocation` section.

## Required Motion.js Update

### Current Issue

The Webflow template currently only imports basic Motion.js functions:

```javascript
import { animate, hover, press } from 'https://cdn.jsdelivr.net/npm/motion@latest/+esm';
```

### Required Update

To enable scroll-triggered animations, you need to add `inView` to the import:

**Location:** `Modelo - Webflow/index.html` (around line 4095)

**Current Code:**
```html
<script type="module">
  import { animate, hover, press } from 'https://cdn.jsdelivr.net/npm/motion@latest/+esm';
  window.Motion = {
    animate,
    hover,
    press,
  };
</script>
```

**Updated Code:**
```html
<script type="module">
  import { animate, hover, press, inView } from 'https://cdn.jsdelivr.net/npm/motion@latest/+esm';
  window.Motion = {
    animate,
    hover,
    press,
    inView,
  };
</script>
```

## How to Make This Change

### ⚠️ Important: Webflow Export Process

Since the `Modelo - Webflow` folder is **READ-ONLY** and must follow the established Webflow policy:

1. **Open your Webflow project** in the Webflow Designer
2. **Go to Project Settings > Custom Code**
3. **Update the Footer Code** to include the new import:

```html
<script type="module">
  import { animate, hover, press, inView } from 'https://cdn.jsdelivr.net/npm/motion@latest/+esm';
  window.Motion = {
    animate,
    hover,
    press,
    inView,
  };
</script>
```

4. **Publish your site**
5. **Export the updated files** from Webflow
6. **Replace the entire `Modelo - Webflow` folder** with the new export

## Animation Features

### Trigger Behavior

- **Target Element:** `.componente-alocao-float`
- **Trigger Section:** `._3-section-patrimonio-alocation`
- **Trigger Point:** When 50% of the section is visible in viewport
- **Trigger Margin:** -10% top/bottom for better UX

### Animation Sequence

**Enter Animation (when scrolling into section):**
1. **Initial State:** Hidden, scaled down, blurred
2. **Phase 1 (0.3s):** Opacity and blur fade in
3. **Phase 2 (0.6s):** Scale up and slide in from below
4. **Final State:** Visible and ready for interaction

**Exit Animation (when scrolling out of section):**
1. **Fade out with scale down and blur** (0.4s)
2. **Component hidden until next entry**

### Animation Properties

```javascript
// Initial State
{
  opacity: 0,
  y: 30,
  filter: 'blur(8px)',
  transform: 'translate(-50%, 0px) scale(0.8)' // Preserves CSS centering
}

// Final State
{
  opacity: 1,
  y: 0,
  filter: 'blur(0px)',
  transform: 'translate(-50%, 0px) scale(1)' // Preserves CSS centering
}
```

## System Integration

The `ScrollFloatAnimationSystem` is automatically integrated into the main application:

### Initialization Order

The system initializes after core systems but before optional features:

1. `attributeFixer`
2. `currencyFormatting`
3. `patrimonySync`
4. `currencyControl`
5. `simpleSync`
6. `chartAnimation`
7. `motionAnimation`
8. `productSystem`
9. `iaToggle`
10. **`scrollFloatAnimation`** ← Added here
11. `sectionVisibility`
12. `openaiAllocation`

### Global API Access

Once initialized, you can access the system via:

```javascript
// Check system status
console.log(ReinoCalculator.animation.scrollFloat.getStatus());

// Manual controls
ReinoCalculator.animation.scrollFloat.forceShow();
ReinoCalculator.animation.scrollFloat.forceHide();
ReinoCalculator.animation.scrollFloat.reset();
```

## Debugging

### System Status Check

```javascript
const status = ReinoCalculator.animation.scrollFloat.getStatus();
console.log('Scroll Float Animation Status:', status);
```

Expected output when working correctly:
```javascript
{
  isInitialized: true,
  hasMotion: true,
  hasInView: true,          // This should be true after the update
  hasFloatComponent: true,
  hasTargetSection: true,
  isAnimated: false,        // true after animation triggers
  hasInViewCleanup: true
}
```

### Common Issues

1. **`hasInView: false`** = Motion.js import needs to be updated
2. **`hasFloatComponent: false`** = Element `.componente-alocao-float` not found
3. **`hasTargetSection: false`** = Element `._3-section-patrimonio-alocation` not found
4. **`hasMotion: false`** = Motion.js not loaded yet or import failed

### Manual Testing

```javascript
// Force show animation (bypass scroll trigger)
ReinoCalculator.animation.scrollFloat.forceShow();

// Reset and test again
ReinoCalculator.animation.scrollFloat.reset();

// Hide manually
ReinoCalculator.animation.scrollFloat.forceHide();
```

## Event Integration

The system dispatches events through the EventCoordinator:

```javascript
// Listen for animation events
document.addEventListener('scrollFloatAnimated', (e) => {
  console.log('Float component entered and animated:', e.detail);
});

document.addEventListener('scrollFloatExited', (e) => {
  console.log('Float component exited:', e.detail);
});
```

## Performance Considerations

- Uses native `inView` from Motion.js for efficient scroll detection
- Animation triggers every time user enters/exits the section
- Preserves CSS centering (`transform: translate(-50%)`) during animations
- Minimal performance impact with `amount: 0.5` threshold
- Cleanup properly handled on system disposal

## Browser Support

- **Modern browsers** with ES6+ support
- **IntersectionObserver API** support (used by Motion.js inView)
- **CSS transforms and filters** support

## Troubleshooting

### Animation Not Triggering

1. Check console for errors
2. Verify Motion.js import includes `inView`
3. Confirm target elements exist in DOM
4. Test scroll position reaches trigger threshold
5. Verify component exits when scrolling away from section

### Animation Not Exiting

1. Check if component properly hides when leaving section
2. Verify floating animation stops when exiting
3. Test re-entry after scrolling away and back

### Animation Appears Too Early/Late

Adjust the margin in the inView options:

```javascript
// In scroll-float-animation.js, line ~89
{
  amount: 0.5,
  margin: '-20% 0px -20% 0px', // Adjust these values
}
```

### Centering Issues

If the component appears off-center during animation, verify the CSS contains:

```css
.componente-alocao-float {
  position: fixed;
  inset: auto auto 56px 50%;
  transform: translate(-50%); /* This centers the component */
}
```

The animation preserves this centering by using `transform: 'translate(-50%, 0px) scale(X)'`

---

**Next Steps:** 
1. Update Motion.js import in Webflow
2. Export and replace Webflow folder
3. Test the scroll animation
4. Fine-tune trigger points if needed