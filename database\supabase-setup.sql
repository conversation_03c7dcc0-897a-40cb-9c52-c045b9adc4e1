-- SQL para criar a tabela no Supabase
-- Execute este SQL no painel do Supabase (SQL Editor)

-- <PERSON><PERSON>, remover a tabela se já existir (cuidado em produção!)
DROP TABLE IF EXISTS calculator_submissions;

-- Criar a tabela novamente
CREATE TABLE calculator_submissions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Dados básicos do formulário
  patrimonio numeric NOT NULL,
  ativos_escolhidos jsonb,
  alocacao jsonb,
  
  -- Metadados da submissão
  submitted_at timestamp with time zone DEFAULT now(),
  user_agent text,
  session_id text,
  
  -- Dados calculados
  total_alocado numeric DEFAULT 0,
  percentual_alocado numeric DEFAULT 0,
  patrimonio_restante numeric DEFAULT 0,
  
  -- Timestamps automáticos
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Criar índices para melhor performance
CREATE INDEX idx_calculator_submissions_submitted_at ON calculator_submissions(submitted_at);
CREATE INDEX idx_calculator_submissions_session_id ON calculator_submissions(session_id);
CREATE INDEX idx_calculator_submissions_patrimonio ON calculator_submissions(patrimonio);

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_calculator_submissions_updated_at
    BEFORE UPDATE ON calculator_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- CONFIGURAR RLS PARA PERMITIR INSERÇÕES PÚBLICAS
-- Ativar Row Level Security
ALTER TABLE calculator_submissions ENABLE ROW LEVEL SECURITY;

-- Política para permitir inserção de qualquer usuário (calculadora pública)
CREATE POLICY "Allow public inserts" ON calculator_submissions
  FOR INSERT WITH CHECK (true);

-- Política para permitir leitura apenas de administradores (opcional)
-- Se quiser que qualquer um possa ler também, mude para: WITH CHECK (true)
CREATE POLICY "Allow admin reads" ON calculator_submissions
  FOR SELECT USING (false); -- Mudar para 'true' se quiser leitura pública

-- Comentários para documentação
COMMENT ON TABLE calculator_submissions IS 'Armazena submissões da calculadora patrimonial';
COMMENT ON COLUMN calculator_submissions.patrimonio IS 'Valor total do patrimônio informado pelo usuário';
COMMENT ON COLUMN calculator_submissions.ativos_escolhidos IS 'Array de ativos selecionados pelo usuário';
COMMENT ON COLUMN calculator_submissions.alocacao IS 'Objeto com a alocação detalhada de cada ativo';
COMMENT ON COLUMN calculator_submissions.total_alocado IS 'Soma total dos valores alocados';
COMMENT ON COLUMN calculator_submissions.percentual_alocado IS 'Percentual do patrimônio que foi alocado';
COMMENT ON COLUMN calculator_submissions.patrimonio_restante IS 'Valor restante não alocado';
