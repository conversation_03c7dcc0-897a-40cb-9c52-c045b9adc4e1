import { expect, test } from '@playwright/test';

// Extend Window interface for TypeScript
declare global {
  interface Window {
    ReinoCalculator?: {
      isInitialized?: boolean;
      systems?: {
        stepNavigation?: {
          goToNext?: () => Promise<boolean>;
          goToPrevious?: () => Promise<boolean>;
        };
        webflowButton?: {
          handleNextNavigation?: () => Promise<void>;
          handleDataSubmission?: () => Promise<void>;
        };
      };
    };
  }
}

test.describe('Webflow Button Integration - Local Development', () => {
  test.beforeEach(async ({ page }) => {
    // Set up console logging
    page.on('console', (msg) => console.log(`[BROWSER]: ${msg.text()}`));
    page.on('pageerror', (error) => console.error(`[PAGE ERROR]: ${error.message}`));

    // Navigate to local Webflow file with modules loaded from localhost:3000
    await page.goto('file:///C:/Users/<USER>/Desktop/Integração Reino/app-calc-reino/Modelo - Webflow/index.html');

    // Wait for DOM to be loaded
    await page.waitForLoadState('domcontentloaded');

    // Wait for our JavaScript modules to load from localhost:3000
    try {
      await page.waitForFunction(
        () => {
          return window.ReinoCalculator && window.ReinoCalculator.isInitialized;
        },
        { timeout: 10000 }
      );
      console.log('✅ ReinoCalculator modules loaded successfully');
    } catch (error) {
      console.log('⚠️ ReinoCalculator modules failed to load:', error);
      throw error;
    }
  });
          constructor() {
            this.isInitialized = false;
          }
          
          async init(stepNavigationSystem) {
            this.stepNavigationSystem = stepNavigationSystem;
            this.setupNextButtons();
            this.setupSendButton();
            this.isInitialized = true;
          }
          
          setupNextButtons() {
            const nextButtons = document.querySelectorAll('[element-function="next"]');
            console.log('Found next buttons:', nextButtons.length);
            
            nextButtons.forEach((button, index) => {
              button.style.border = '2px solid #4CAF50';
              button.setAttribute('data-webflow-button', 'next');
              
              button.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Next button clicked:', index);
              });
            });
          }
          
          setupSendButton() {
            const sendButton = document.querySelector('[element-function="send"]');
            if (sendButton) {
              sendButton.style.border = '2px solid #FF9800';
              sendButton.setAttribute('data-webflow-button', 'send');
              
              sendButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Send button clicked');
              });
            }
          }
        }
        
        // Initialize mock system
        window.ReinoCalculator.systems.webflowButton = new MockWebflowButtonSystem();
        window.ReinoCalculator.systems.webflowButton.init();
      `,
    });

    // Wait for our mock system to initialize
    await page.waitForTimeout(1000);
  });

  test('should find and verify button structure on production site', async ({ page }) => {
    // Test 1: Verify the production site structure
    const title = await page.title();
    console.log('Site title:', title);

    // Check if this is the correct site
    expect(title).toContain('Grupo');

    // Test 2: Find buttons with if-element="button-next" (the actual implementation)
    const ifElementButtons = await page.locator('[if-element="button-next"]').count();
    console.log(`Found ${ifElementButtons} if-element buttons`);

    // Test 3: Check sections with data-step attributes
    const stepSections = await page.locator('[data-step]').count();
    console.log(`Found ${stepSections} step sections`);

    // Test 4: Verify visual indicators were added by our mock
    if (ifElementButtons > 0) {
      const firstButton = page.locator('[if-element="button-next"]').first();
      await expect(firstButton).toBeVisible();
      console.log('if-element button is visible');
    }

    // Since the production site doesn't have element-function attributes,
    // our mock system needs to find and enhance the existing buttons
    const enhancedButtons = await page.evaluate(() => {
      const buttons = document.querySelectorAll('[if-element="button-next"]');
      return Array.from(buttons).map((btn, index) => ({
        index,
        hasDataAttribute: btn.hasAttribute('data-webflow-button'),
        className: btn.className,
      }));
    });

    console.log('Enhanced buttons:', enhancedButtons);
  });

  test('should find and setup send button correctly', async ({ page }) => {
    // Check that send button exists
    const sendButton = page.locator('[element-function="send"]');
    await expect(sendButton).toBeVisible();

    // Check if debug mode styling is applied
    const borderStyle = await sendButton.evaluate((el) => window.getComputedStyle(el).border);
    expect(borderStyle).toContain('rgb(255, 152, 0)'); // Orange border for debug
  });

  test('should navigate from intro to money section', async ({ page }) => {
    // Start on intro section (step 0)
    const introSection = page.locator('[data-step="0"]');
    await expect(introSection).toHaveClass(/active/);

    // Click the first next button (in intro section)
    const firstNextButton = page.locator('[data-step="0"] [element-function="next"]');
    await firstNextButton.click();

    // Wait for navigation
    await page.waitForTimeout(500);

    // Check that we moved to money section (step 1)
    const moneySection = page.locator('[data-step="1"]');
    await expect(moneySection).toHaveClass(/active/);

    // Check that intro section is no longer active
    await expect(introSection).not.toHaveClass(/active/);
  });

  test('should validate money input before proceeding to assets section', async ({ page }) => {
    // Navigate to money section first
    await page.locator('[data-step="0"] [element-function="next"]').click();
    await page.waitForTimeout(500);

    // Try to proceed without entering money value
    const moneyNextButton = page.locator('[data-step="1"] [element-function="next"]');
    await moneyNextButton.click();

    // Should show validation error toast
    const errorToast = page.locator('.webflow-toast--error');
    await expect(errorToast).toBeVisible();
    await expect(errorToast).toContainText('complete os campos obrigatórios');

    // Should still be on money section
    const moneySection = page.locator('[data-step="1"]');
    await expect(moneySection).toHaveClass(/active/);
  });

  test('should proceed to assets section with valid money input', async ({ page }) => {
    // Navigate to money section
    await page.locator('[data-step="0"] [element-function="next"]').click();
    await page.waitForTimeout(500);

    // Enter a valid money value
    const moneyInput = page.locator('[is-main="true"]');
    await moneyInput.fill('100000,00');

    // Click next button
    const moneyNextButton = page.locator('[data-step="1"] [element-function="next"]');
    await moneyNextButton.click();

    // Wait for navigation
    await page.waitForTimeout(500);

    // Should be on assets section (step 2)
    const assetsSection = page.locator('[data-step="2"]');
    await expect(assetsSection).toHaveClass(/active/);
  });

  test('should validate asset selection before proceeding to allocation', async ({ page }) => {
    // Navigate to assets section
    await page.locator('[data-step="0"] [element-function="next"]').click();
    await page.waitForTimeout(300);

    // Add money value
    await page.locator('[is-main="true"]').fill('100000,00');
    await page.locator('[data-step="1"] [element-function="next"]').click();
    await page.waitForTimeout(300);

    // Try to proceed without selecting any assets
    const assetsNextButton = page.locator('[data-step="2"] [element-function="next"]');
    await assetsNextButton.click();

    // Should show validation error
    const errorToast = page.locator('.webflow-toast--error');
    await expect(errorToast).toBeVisible();

    // Should still be on assets section
    const assetsSection = page.locator('[data-step="2"]');
    await expect(assetsSection).toHaveClass(/active/);
  });

  test('should proceed to allocation with selected assets', async ({ page }) => {
    // Navigate through steps and select assets
    await page.locator('[data-step="0"] [element-function="next"]').click();
    await page.waitForTimeout(300);

    await page.locator('[is-main="true"]').fill('100000,00');
    await page.locator('[data-step="1"] [element-function="next"]').click();
    await page.waitForTimeout(300);

    // Select some assets (assuming asset selection system is working)
    const firstAsset = page.locator('[ativo-product]').first();
    await firstAsset.click();

    // Wait for asset selection to register
    await page.waitForTimeout(500);

    // Click next to proceed to allocation
    const assetsNextButton = page.locator('[data-step="2"] [element-function="next"]');
    await assetsNextButton.click();

    // Wait for navigation
    await page.waitForTimeout(500);

    // Should be on allocation section (step 3)
    const allocationSection = page.locator('[data-step="3"]');
    await expect(allocationSection).toHaveClass(/active/);
  });

  test('should collect and validate form data before sending', async ({ page }) => {
    // Navigate to final section
    await page.locator('[data-step="0"] [element-function="next"]').click();
    await page.waitForTimeout(300);

    await page.locator('[is-main="true"]').fill('100000,00');
    await page.locator('[data-step="1"] [element-function="next"]').click();
    await page.waitForTimeout(300);

    // Select an asset
    const firstAsset = page.locator('[ativo-product]').first();
    await firstAsset.click();
    await page.waitForTimeout(300);

    await page.locator('[data-step="2"] [element-function="next"]').click();
    await page.waitForTimeout(500);

    // Click send button
    const sendButton = page.locator('[element-function="send"]');
    await sendButton.click();

    // Should show success message since we're in debug mode
    const successToast = page.locator('.webflow-toast--success');
    await expect(successToast).toBeVisible();
    await expect(successToast).toContainText('Dados enviados com sucesso');
  });

  test('should show debug mode indicator', async ({ page }) => {
    // Check that debug mode indicator is visible
    const debugIndicator = page.locator('div:has-text("🔧 DEBUG MODE")');
    await expect(debugIndicator).toBeVisible();
  });

  test('should disable and re-enable send button during submission', async ({ page }) => {
    // Navigate to final section (quick path)
    await page.locator('[data-step="0"] [element-function="next"]').click();
    await page.waitForTimeout(300);

    await page.locator('[is-main="true"]').fill('100000,00');
    await page.locator('[data-step="1"] [element-function="next"]').click();
    await page.waitForTimeout(300);

    const firstAsset = page.locator('[ativo-product]').first();
    await firstAsset.click();
    await page.waitForTimeout(300);

    await page.locator('[data-step="2"] [element-function="next"]').click();
    await page.waitForTimeout(500);

    // Get send button
    const sendButton = page.locator('[element-function="send"]');

    // Click send button
    await sendButton.click();

    // Button should be disabled temporarily
    await expect(sendButton).toBeDisabled();

    // Button text should change to "Enviando..."
    await expect(sendButton.locator('div').first()).toContainText('Enviando...');

    // After submission completes, button should be re-enabled
    await page.waitForTimeout(2000); // Wait for simulated async operation
    await expect(sendButton).toBeEnabled();
    await expect(sendButton.locator('div').first()).toContainText('Enviar');
  });
});

test.describe('Error Handling', () => {
  test('should handle navigation errors gracefully', async ({ page }) => {
    await page.goto('https://grupos-groovy-site-fc802f.webflow.io/');
    await page.waitForLoadState('networkidle');

    // Inject error condition
    await page.evaluate(() => {
      // Remove step navigation system to simulate error
      if (window.ReinoCalculator?.systems) {
        window.ReinoCalculator.systems.stepNavigation = undefined;
      }
    });

    // Try to navigate
    const nextButton = page.locator('[element-function="next"]').first();
    await nextButton.click();

    // Should show error toast
    const errorToast = page.locator('.webflow-toast--error');
    await expect(errorToast).toBeVisible();
    await expect(errorToast).toContainText('Erro ao navegar');
  });

  test('should handle missing sections gracefully', async ({ page }) => {
    await page.goto('https://grupos-groovy-site-fc802f.webflow.io/');
    await page.waitForLoadState('networkidle');

    // Remove a section to simulate error
    await page.evaluate(() => {
      const section = document.querySelector('[data-step="1"]');
      if (section) section.remove();
    });

    // Try to navigate
    const nextButton = page.locator('[element-function="next"]').first();
    await nextButton.click();

    // Should handle gracefully (may fallback to manual navigation)
    await page.waitForTimeout(1000);

    // Check console for warnings
    const consoleMessages: string[] = [];
    page.on('console', (msg) => consoleMessages.push(msg.text()));

    // Should log warning about missing step
    await page.waitForTimeout(500);
    expect(
      consoleMessages.some((msg) => msg.includes('Step') && msg.includes('not found'))
    ).toBeTruthy();
  });
});
