/**
 * Chart Animation System
 * Handles GSAP-based chart animations synchronized with range sliders
 * Updated to work with both legacy chart system and new ativos-grafico-item structure
 */
class ChartAnimationSystem {
  constructor() {
    this.isInitialized = false;
    this.timeline = null;
    this.ativosTimeline = null;
    this.ativosItems = new Map();
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    document.addEventListener('DOMContentLoaded', () => {
      this.waitForGSAP();
    });

    this.isInitialized = true;
  }

  waitForGSAP() {
    if (window.gsap) {
      this.initializeChartSystem();
    } else {
      setTimeout(() => this.waitForGSAP(), 50);
    }
  }

  initializeChartSystem() {
    // Initialize legacy chart system
    if (window.$) {
      this.initializeWithJQuery();
    } else {
      this.initializeVanilla();
    }

    // Initialize new ativos-grafico system
    this.initializeAtivosGraficoSystem();
  }

  initializeWithJQuery() {
    const { $ } = window;

    // Inicializa o gráfico único
    $('.chart_wrap').each((index, element) => {
      this.timeline = window.gsap.timeline({
        paused: true,
      });

      $(element)
        .find('.chart_column')
        .each((columnIndex, columnElement) => {
          let bar = $(columnElement).find('.chart_bar');
          let start = +$(columnElement).attr('start');
          let end = +$(columnElement).attr('end');
          let numberText = $(columnElement).find('.chart_number');
          let number = { value: start };

          this.timeline
            .fromTo(
              bar,
              {
                height: start + '%',
              },
              {
                height: end + '%',
              },
              '<'
            )
            .fromTo(
              number,
              {
                value: start,
              },
              {
                value: end,
                onUpdate: () => numberText.text(Math.round(number.value)),
              },
              '<'
            );
        });
    });

    // Conecta TODOS os sliders ao mesmo gráfico
    $('range-slider.slider').each((index, element) => {
      // CORRIGE O STEP PROBLEMÁTICO
      element.setAttribute('step', '0.01'); // Muda de 0.00000001 para 0.01

      element.addEventListener('input', () => {
        if (this.timeline) {
          this.timeline.progress(element.value);
        }
      });
    });
  }

  initializeAtivosGraficoSystem() {
    // DISABLED: ChartAnimationSystem conflicts with SimpleSyncSystem
    // The SimpleSyncSystem now handles all ativos-grafico-item synchronization
    console.log(
      '🚫 ChartAnimationSystem: ativos-grafico-item initialization DISABLED to prevent conflicts with SimpleSyncSystem'
    );
    console.log(
      '📊 SimpleSyncSystem now handles all visual synchronization for ativos-grafico-item elements'
    );
    return;
  }

  addAtivoToTimeline(item, index) {
    const { barraElement, porcentagemElement, targetPercentage } = item;

    // Set initial state
    window.gsap.set(barraElement, { width: '0%' });

    // Create number animation object
    const numberObj = { value: 0 };

    // Add bar animation to timeline
    this.ativosTimeline.fromTo(
      barraElement,
      { width: '0%' },
      {
        width: `${targetPercentage}%`,
        duration: 0.8,
        ease: 'power2.out',
      },
      index * 0.1
    );

    // Add number animation to timeline
    this.ativosTimeline.fromTo(
      numberObj,
      { value: 0 },
      {
        value: targetPercentage,
        duration: 0.8,
        ease: 'power2.out',
        onUpdate: () => {
          const currentValue = Math.round(numberObj.value * 10) / 10;
          porcentagemElement.textContent = `${currentValue}%`;
        },
      },
      index * 0.1
    );
  }

  connectAtivosToSliders() {
    // DISABLED: Prevents conflicts with SimpleSyncSystem
    console.log(
      '🚫 ChartAnimationSystem: slider connections DISABLED to prevent conflicts with SimpleSyncSystem'
    );
    return;
  }

  updateAtivoPercentage(category, product, percentage) {
    // DISABLED: SimpleSyncSystem now handles all ativos-grafico-item updates
    console.log(
      `🚫 ChartAnimationSystem: updateAtivoPercentage DISABLED for ${category}-${product}. SimpleSyncSystem handles this.`
    );
    return;
  }

  initializeVanilla() {
    // Vanilla JavaScript implementation
    const chartWraps = document.querySelectorAll('.chart_wrap');

    chartWraps.forEach((chartWrap) => {
      this.timeline = window.gsap.timeline({
        paused: true,
      });

      const chartColumns = chartWrap.querySelectorAll('.chart_column');

      chartColumns.forEach((column) => {
        const bar = column.querySelector('.chart_bar');
        const start = parseInt(column.getAttribute('start')) || 0;
        const end = parseInt(column.getAttribute('end')) || 0;
        const numberText = column.querySelector('.chart_number');
        const number = { value: start };

        if (bar && numberText) {
          this.timeline
            .fromTo(
              bar,
              {
                height: start + '%',
              },
              {
                height: end + '%',
              },
              '<'
            )
            .fromTo(
              number,
              {
                value: start,
              },
              {
                value: end,
                onUpdate: () => {
                  numberText.textContent = Math.round(number.value);
                },
              },
              '<'
            );
        }
      });
    });

    // Conecta TODOS os sliders ao mesmo gráfico
    const sliders = document.querySelectorAll('range-slider.slider');

    sliders.forEach((slider) => {
      // CORRIGE O STEP PROBLEMÁTICO
      slider.setAttribute('step', '0.01'); // Muda de 0.00000001 para 0.01

      slider.addEventListener('input', () => {
        if (this.timeline) {
          this.timeline.progress(slider.value);
        }
      });
    });
  }

  // Public API methods
  playChart() {
    if (this.timeline) {
      this.timeline.play();
    }
  }

  playAtivosChart() {
    if (this.ativosTimeline) {
      this.ativosTimeline.play();
    }
  }

  playAllCharts() {
    this.playChart();
    this.playAtivosChart();
  }

  pauseChart() {
    if (this.timeline) {
      this.timeline.pause();
    }
  }

  resetChart() {
    if (this.timeline) {
      this.timeline.progress(0);
    }
  }

  resetAtivosChart() {
    // DISABLED: SimpleSyncSystem handles resets
    console.log(
      '🚫 ChartAnimationSystem: resetAtivosChart DISABLED. Use SimpleSyncSystem.resetAll() instead.'
    );
    return;
  }

  resetAllCharts() {
    this.resetChart();
    this.resetAtivosChart();
  }

  setProgress(progress) {
    if (this.timeline && progress >= 0 && progress <= 1) {
      this.timeline.progress(progress);
    }
  }

  getProgress() {
    return this.timeline ? this.timeline.progress() : 0;
  }

  isPlaying() {
    return this.timeline ? this.timeline.isActive() : false;
  }

  getDuration() {
    return this.timeline ? this.timeline.duration() : 0;
  }

  // Advanced animation controls
  seekTo(time) {
    if (this.timeline && time >= 0) {
      this.timeline.seek(time);
    }
  }

  setTimeScale(scale) {
    if (this.timeline && scale > 0) {
      this.timeline.timeScale(scale);
    }
  }

  reverse() {
    if (this.timeline) {
      this.timeline.reverse();
    }
  }

  restart() {
    if (this.timeline) {
      this.timeline.restart();
    }
  }

  // Event handlers for external control
  onComplete(callback) {
    if (this.timeline && typeof callback === 'function') {
      this.timeline.eventCallback('onComplete', callback);
    }
  }

  onUpdate(callback) {
    if (this.timeline && typeof callback === 'function') {
      this.timeline.eventCallback('onUpdate', callback);
    }
  }

  onStart(callback) {
    if (this.timeline && typeof callback === 'function') {
      this.timeline.eventCallback('onStart', callback);
    }
  }

  // Cleanup
  destroy() {
    if (this.timeline) {
      this.timeline.kill();
      this.timeline = null;
    }

    if (this.ativosTimeline) {
      this.ativosTimeline.kill();
      this.ativosTimeline = null;
    }

    this.ativosItems.clear();
    this.isInitialized = false;
  }

  // New methods for ativos system integration

  getAtivoItem(category, product) {
    const key = `${category}_${product}`.replace(/\s+/g, '_');
    return this.ativosItems.get(key);
  }

  getAllAtivosItems() {
    return Array.from(this.ativosItems.values());
  }

  setAtivosProgress(progress) {
    // DISABLED: SimpleSyncSystem handles progress
    console.log(
      '🚫 ChartAnimationSystem: setAtivosProgress DISABLED. SimpleSyncSystem handles visual updates.'
    );
    return;
  }

  getAtivosProgress() {
    // DISABLED: SimpleSyncSystem handles progress tracking
    console.log(
      '🚫 ChartAnimationSystem: getAtivosProgress DISABLED. Check SimpleSyncSystem for progress.'
    );
    return 0;
  }
}

// Export for use in other modules
export { ChartAnimationSystem };
