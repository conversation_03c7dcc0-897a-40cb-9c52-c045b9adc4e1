/**
 * Supabase Configuration
 * Configure your Supabase project credentials here
 */

import { createClient } from '@supabase/supabase-js';

// Supabase project configuration
const SUPABASE_URL = 'https://dwpsyresppubuxbrwrkc.supabase.co'; // Replace with your project URL
const SUPABASE_ANON_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR3cHN5cmVzcHB1YnV4YnJ3cmtjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNjcxNzgsImV4cCI6MjA2ODk0MzE3OH0.Z0sA04rkEBVGnQqmHy8UO7FCzYjCCsG7ENCBuY4Ijbc'; // Replace with your anon key

// Create Supabase client
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Database table name
export const TABLE_NAME = 'calculator_submissions';

// Schema for the data structure
export const DATA_SCHEMA = {
  // ID automático do Supabase
  id: 'uuid',

  // Dados básicos
  patrimonio: 'numeric',
  ativos_escolhidos: 'jsonb',
  alocacao: 'jsonb',

  // Metadados
  submitted_at: 'timestamp with time zone',
  user_agent: 'text',
  session_id: 'text',

  // Dados calculados
  total_alocado: 'numeric',
  percentual_alocado: 'numeric',
  patrimonio_restante: 'numeric',
};

// Helper function to validate environment
export function validateSupabaseConfig() {
  if (SUPABASE_URL === 'YOUR_SUPABASE_URL' || SUPABASE_ANON_KEY === 'YOUR_SUPABASE_ANON_KEY') {
    console.error(
      '❌ Supabase not configured. Please update src/config/supabase.js with your credentials.'
    );
    return false;
  }
  return true;
}

// Export default client for convenience
export default supabase;
