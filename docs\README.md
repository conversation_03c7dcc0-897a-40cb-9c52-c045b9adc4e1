# 📚 Documentação do Projeto App-Calc-Reino

Esta pasta contém toda a ### 🚀 [Deploy/](./Deploy/)
Guias de deployment e produção:

- Deployment Guide

## 🗂️ Navegação Rápida

| Categoria | Descrição | Principais Arquivos |
|-----------|-----------|-------------------|
| **Integrations** | Conectores externos | Supabase, Typebot, Salesforce |
| **Setup** | Configuração inicial | Checklists, Patterns |
| **Troubleshooting** | Resolução de problemas | Debug, Analysis, Conflicts |
| **Architecture** | Estrutura do sistema | Modular docs |
| **UI-UX** | Interface do usuário | Animations, Improvements |
| **Assets** | Gestão de recursos | Filters, Mappings |
| **Forms** | Formulários | Navigation, Setup |
| **AI** | Inteligência Artificial | Prompts, Integration |
| **Deploy** | Produção | Deployment guides |

## 🔧 Comandos Disponíveis

```bash
# Validar conformidade da política Webflow
npm run validate:webflow

# Comandos padrão de desenvolvimento
npm run dev          # Servidor de desenvolvimento
npm run build        # Build de produção
npm run lint         # Linting do código
npm run test         # Executar testes
```

## 📋 Como Usar Esta Documentação

1. **Para desenvolvedores novos**: Comece com [Setup/](./Setup/) e [Architecture/](./Architecture/)
2. **Para integrações**: Consulte [Integrations/](./Integrations/)
3. **Para problemas**: Verifique [Troubleshooting/](./Troubleshooting/)
4. **Para deploy**: Siga [Deploy/](./Deploy/)
5. **Para mudanças na UI**: Consulte [UI-UX/](./UI-UX/)

## 🎯 Primeiros Passos

1. **Leia a Política Webflow**: Comece com [WEBFLOW_POLICY.md](../WEBFLOW_POLICY.md)
2. **Entenda os Padrões**: Revise [Setup/WEBFLOW_FILE_PATTERNS.md](./Setup/WEBFLOW_FILE_PATTERNS.md)
3. **Siga o Fluxo de Desenvolvimento**: Veja [README principal](../README.md)
4. **Valide a Configuração**: Execute `npm run validate:webflow`

## 🔄 Manutenção da Documentação

Ao atualizar a documentação:

- Mantenha cada categoria bem organizada
- Atualize este README quando adicionar novas pastas
- Use nomes descritivos para os arquivos
- Inclua datas nas análises de bugs
- Mantenha referências cruzadas claras entre documentos

## 🔍 Precisa de Ajuda?

- **Mudanças no Webflow**: Use a plataforma Webflow, depois exporte
- **Questões sobre Política**: Revise [WEBFLOW_POLICY.md](../WEBFLOW_POLICY.md)
- **Problemas Técnicos**: Verifique com `npm run validate:webflow`
- **Estrutura de Arquivos**: Consulte [Setup/WEBFLOW_FILE_PATTERNS.md](./Setup/WEBFLOW_FILE_PATTERNS.md)

---

**📌 Última atualização da estrutura**: Agosto 2025  
**🔗 Lembre-se: A plataforma Webflow é a fonte da verdade para todas as mudanças de template**umentação técnica organizada por categorias para facilitar a navegação e manutenção.

## � Políticas Importantes

### Core Policies

- [**WEBFLOW_POLICY.md**](../WEBFLOW_POLICY.md) - **LEIA PRIMEIRO** - Política crítica sobre template Webflow somente leitura
- [Modelo - Webflow/README.md](../Modelo%20-%20Webflow/README.md) - Aviso direto e diretrizes da pasta

### Webflow Quick Reference

#### ❌ NUNCA FAÇA

- Editar arquivos em `Modelo - Webflow/` diretamente
- Adicionar/remover arquivos da pasta Webflow manualmente
- Modificar HTML, CSS ou JS nos templates

#### ✅ FLUXO CORRETO

1. Fazer mudanças na **plataforma Webflow**
2. **Exportar** arquivos atualizados do Webflow
3. **Substituir toda** a pasta `Modelo - Webflow`
4. **Fazer commit** da substituição completa

## 📁 Estrutura Organizada

### � [Integrations/](./Integrations/)

Documentação sobre integrações com serviços externos:

- Supabase Integration
- DGM Canvas Integration  
- Salesforce Setup Guide
- Typebot Integration & Implementation
- Typebot Solution Final

### ⚙️ [Setup/](./Setup/)

Guias de configuração e setup inicial:

- Checklist Typebot Setup
- Webflow File Patterns

### 🔧 [Troubleshooting/](./Troubleshooting/)

Análises de bugs, debugging e soluções de problemas:

- Debug Typebot Flow
- Typebot Data Encryption Issue
- Sync Bug Analysis Complete
- Chart Conflict Resolution
- Attribute Mismatch Analysis

### 🏗️ [Architecture/](./Architecture/)

Documentação sobre arquitetura e estrutura do sistema:

- Modular Structure
- Modular README

### 🎨 [UI-UX/](./UI-UX/)

Documentação sobre interface e experiência do usuário:

- Scroll Float Animation Setup
- Melhorias de Patrimônio

### 📦 [Assets/](./Assets/)

Gestão de recursos e mapeamento de dados:

- Asset Selection Filter
- Attribute Mapping

### 📝 [Forms/](./Forms/)

Documentação relacionada a formulários:

- Form-like App (Step Navigation Hybrid, Webflow Setup Guide)

### 🤖 [AI/](./AI/)

Documentação sobre funcionalidades de IA:

- IA Toggle Summary
- Natural Language
- OpenAI Prompt Integration
- Removed AI Code

### � [Deploy/](./Deploy/)

Guias de deployment e produção:

- Deployment Guide

```
docs/
├── README.md                    # This file - Documentation index
├── WEBFLOW_FILE_PATTERNS.md     # Technical file patterns reference
└── [future documentation]      # Additional docs as needed

../
├── WEBFLOW_POLICY.md           # Main policy document
├── Modelo - Webflow/
│   └── README.md               # Folder-specific warnings
└── README.md                   # Project main documentation
```

## 🎯 Getting Started

1. **Read the Webflow Policy**: Start with [WEBFLOW_POLICY.md](../WEBFLOW_POLICY.md)
2. **Understand File Patterns**: Review [WEBFLOW_FILE_PATTERNS.md](./WEBFLOW_FILE_PATTERNS.md)
3. **Follow Development Workflow**: See [Project README](../README.md)
4. **Validate Setup**: Run `npm run validate:webflow`

## 🔍 Need Help?

- **Webflow Changes**: Use Webflow platform, then export
- **Policy Questions**: Review [WEBFLOW_POLICY.md](../WEBFLOW_POLICY.md)
- **Technical Issues**: Check validation with `npm run validate:webflow`
- **File Structure**: Refer to [WEBFLOW_FILE_PATTERNS.md](./WEBFLOW_FILE_PATTERNS.md)

## 📝 Contributing to Documentation

When updating documentation:

- Keep policy documents in sync
- Update file patterns when Webflow structure changes
- Maintain clear cross-references between documents
- Test validation scripts after changes

---

**🔗 Remember: Webflow platform is the source of truth for all template changes**
