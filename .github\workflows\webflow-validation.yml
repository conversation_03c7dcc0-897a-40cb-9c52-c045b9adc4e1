name: Webflow Policy Validation

on:
  push:
    branches: [ main, master, develop ]
    paths:
      - 'Modelo - Webflow/**'
  pull_request:
    branches: [ main, master, develop ]
    paths:
      - 'Modelo - Webflow/**'
  workflow_dispatch:

jobs:
  validate-webflow-policy:
    runs-on: ubuntu-latest
    name: Validate Webflow Template Policy

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run Webflow policy validation
        run: npm run validate:webflow

      - name: Check for unauthorized Webflow changes
        run: |
          echo "🔍 Checking for changes in Webflow template folder..."

          # Check if there are changes in the Webflow folder
          if git diff --name-only HEAD~1 HEAD | grep -q "^Modelo - Webflow/"; then
            echo "📁 Changes detected in Webflow template folder"

            # List the changed files
            echo "📝 Changed files:"
            git diff --name-only HEAD~1 HEAD | grep "^Modelo - Webflow/" | sed 's/^/  - /'

            # Check if this looks like a complete folder replacement (preferred)
            CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD | grep "^Modelo - Webflow/" | wc -l)
            TOTAL_FILES=$(find "Modelo - Webflow" -type f | wc -l)

            echo "📊 Statistics:"
            echo "  - Changed files: $CHANGED_FILES"
            echo "  - Total files in folder: $TOTAL_FILES"

            # If only a few files changed, it might be a direct edit (violation)
            if [ "$CHANGED_FILES" -lt 3 ] && [ "$TOTAL_FILES" -gt 5 ]; then
              echo "⚠️  WARNING: Only a few files changed. This might indicate direct editing."
              echo "⚠️  Preferred workflow: Replace entire folder with Webflow export"
              echo ""
              echo "🔗 If this is a legitimate Webflow export with few changes, you can ignore this warning."
              echo "🔗 If this was direct editing, please:"
              echo "   1. Revert these changes"
              echo "   2. Make changes in Webflow platform"
              echo "   3. Export and replace the entire folder"
            else
              echo "✅ Change pattern suggests proper Webflow export workflow"
            fi

            # Show the actual changes for review
            echo ""
            echo "📋 Detailed changes (first 50 lines):"
            git diff HEAD~1 HEAD "Modelo - Webflow/" | head -50

          else
            echo "✅ No changes detected in Webflow template folder"
          fi

      - name: Validate policy documentation
        run: |
          echo "📚 Validating policy documentation..."

          # Check if policy files exist and are not empty
          if [ ! -f "WEBFLOW_POLICY.md" ] || [ ! -s "WEBFLOW_POLICY.md" ]; then
            echo "❌ WEBFLOW_POLICY.md is missing or empty"
            exit 1
          fi

          if [ ! -f "Modelo - Webflow/README.md" ] || [ ! -s "Modelo - Webflow/README.md" ]; then
            echo "❌ Modelo - Webflow/README.md is missing or empty"
            exit 1
          fi

          echo "✅ All policy documentation files are present"

      - name: Policy compliance summary
        run: |
          echo ""
          echo "📋 WEBFLOW POLICY COMPLIANCE SUMMARY"
          echo "================================================"
          echo ""
          echo "✅ Policy validation completed successfully"
          echo "✅ Folder structure is valid"
          echo "✅ Documentation is in place"
          echo ""
          echo "🚨 REMINDER: Webflow template folder is READ-ONLY"
          echo "🔗 Make changes in Webflow platform, then export"
          echo "📖 See WEBFLOW_POLICY.md for complete guidelines"
          echo ""
        if: success()

      - name: Policy violation detected
        run: |
          echo ""
          echo "❌ WEBFLOW POLICY VALIDATION FAILED"
          echo "============================================"
          echo ""
          echo "🚨 The Webflow template folder policy has been violated"
          echo "📖 Please review WEBFLOW_POLICY.md for proper workflow"
          echo "🔗 Contact the development team if you need assistance"
          echo ""
          exit 1
        if: failure()
