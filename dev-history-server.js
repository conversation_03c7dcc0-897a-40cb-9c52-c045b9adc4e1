/**
 * Development History API Server
 * Simple Express server for testing DGM Canvas history integration
 */

import express from "express";
import cors from "cors";

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data for testing (replace with real Supabase integration)
const mockSubmissions = [
  {
    id: "mock-1",
    timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString(), // 10 minutes ago
    patrimonio: {
      total: 150000,
      totalFormatted: "R$ 150.000,00",
      alocado: 120000,
      alocadoFormatted: "R$ 120.000,00",
      restante: 30000,
      restanteFormatted: "R$ 30.000,00",
      percentualAlocado: 80,
      percentualAlocadoFormatted: "80%",
    },
    ativos: {
      escolhidos: [
        { product: "Renda Fixa", category: "renda-fixa" },
        { product: "Ações", category: "renda-variavel" },
      ],
      alocacao: [
        {
          key: "renda-fixa",
          product: "Renda Fixa",
          category: "renda-fixa",
          value: 75000,
          valueFormatted: "R$ 75.000,00",
          percentage: 50,
          percentageFormatted: "50%",
        },
        {
          key: "acoes",
          product: "Ações",
          category: "renda-variavel",
          value: 45000,
          valueFormatted: "R$ 45.000,00",
          percentage: 30,
          percentageFormatted: "30%",
        },
      ],
    },
    usuario: {
      nome: "João Silva",
      email: "<EMAIL>",
      hasUserData: true,
    },
    metadata: {
      source: "mock-data",
      sessionId: "mock-session-1",
    },
  },
  {
    id: "mock-2",
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    patrimonio: {
      total: 250000,
      totalFormatted: "R$ 250.000,00",
      alocado: 200000,
      alocadoFormatted: "R$ 200.000,00",
      restante: 50000,
      restanteFormatted: "R$ 50.000,00",
      percentualAlocado: 80,
      percentualAlocadoFormatted: "80%",
    },
    ativos: {
      escolhidos: [
        { product: "Tesouro Direto", category: "renda-fixa" },
        { product: "FIIs", category: "fundos" },
        { product: "Cripto", category: "alternativos" },
      ],
      alocacao: [
        {
          key: "tesouro",
          product: "Tesouro Direto",
          category: "renda-fixa",
          value: 100000,
          valueFormatted: "R$ 100.000,00",
          percentage: 40,
          percentageFormatted: "40%",
        },
        {
          key: "fiis",
          product: "FIIs",
          category: "fundos",
          value: 70000,
          valueFormatted: "R$ 70.000,00",
          percentage: 28,
          percentageFormatted: "28%",
        },
        {
          key: "cripto",
          product: "Cripto",
          category: "alternativos",
          value: 30000,
          valueFormatted: "R$ 30.000,00",
          percentage: 12,
          percentageFormatted: "12%",
        },
      ],
    },
    usuario: {
      nome: "Maria Santos",
      email: "<EMAIL>",
      hasUserData: true,
    },
    metadata: {
      source: "mock-data",
      sessionId: "mock-session-2",
    },
  },
  {
    id: "mock-3",
    timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
    patrimonio: {
      total: 500000,
      totalFormatted: "R$ 500.000,00",
      alocado: 450000,
      alocadoFormatted: "R$ 450.000,00",
      restante: 50000,
      restanteFormatted: "R$ 50.000,00",
      percentualAlocado: 90,
      percentualAlocadoFormatted: "90%",
    },
    ativos: {
      escolhidos: [
        { product: "Ações Brasil", category: "renda-variavel" },
        { product: "Ações Exterior", category: "renda-variavel" },
        { product: "Renda Fixa", category: "renda-fixa" },
      ],
      alocacao: [
        {
          key: "acoes-br",
          product: "Ações Brasil",
          category: "renda-variavel",
          value: 200000,
          valueFormatted: "R$ 200.000,00",
          percentage: 40,
          percentageFormatted: "40%",
        },
        {
          key: "acoes-ext",
          product: "Ações Exterior",
          category: "renda-variavel",
          value: 150000,
          valueFormatted: "R$ 150.000,00",
          percentage: 30,
          percentageFormatted: "30%",
        },
        {
          key: "renda-fixa",
          product: "Renda Fixa",
          category: "renda-fixa",
          value: 100000,
          valueFormatted: "R$ 100.000,00",
          percentage: 20,
          percentageFormatted: "20%",
        },
      ],
    },
    usuario: {
      nome: "Carlos Costa",
      email: "<EMAIL>",
      hasUserData: true,
    },
    metadata: {
      source: "mock-data",
      sessionId: "mock-session-3",
    },
  },
];

const mockStats = {
  totalSubmissions: 3,
  averagePatrimonio: 300000,
  averageAllocation: 256666.67,
  averageAllocationPercentage: 83.33,
  submissionsToday: 3,
  submissionsThisWeek: 3,
};

// Routes

// GET /api/history - API documentation
app.get("/api/history", (req, res) => {
  res.json({
    success: true,
    message: "DGM Canvas History API - Development Server",
    version: "1.0.0-dev",
    endpoints: {
      recent: "GET /api/history/recent?limit=10",
      stats: "GET /api/history/stats",
      search: "GET /api/history/search?q=term&limit=20",
      single: "GET /api/history/:id",
    },
    timestamp: new Date().toISOString(),
  });
});

// GET /api/history/recent - Get recent submissions
app.get("/api/history/recent", (req, res) => {
  const limit = parseInt(req.query.limit) || 10;
  const recentData = mockSubmissions.slice(
    0,
    Math.min(limit, mockSubmissions.length)
  );

  console.log(`📊 [DevServer] Serving ${recentData.length} recent submissions`);

  res.json({
    success: true,
    data: recentData,
    total: recentData.length,
    timestamp: new Date().toISOString(),
  });
});

// GET /api/history/stats - Get statistics
app.get("/api/history/stats", (req, res) => {
  console.log("📈 [DevServer] Serving statistics");

  res.json({
    success: true,
    data: mockStats,
    timestamp: new Date().toISOString(),
  });
});

// GET /api/history/search - Search submissions
app.get("/api/history/search", (req, res) => {
  const searchTerm = req.query.q;
  const limit = parseInt(req.query.limit) || 20;

  if (!searchTerm) {
    return res.status(400).json({
      success: false,
      error: "q (search term) query parameter is required",
      example: "/api/history/search?q=joao&limit=10",
    });
  }

  const results = mockSubmissions
    .filter(
      (submission) =>
        submission.usuario.nome
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        submission.usuario.email
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase())
    )
    .slice(0, limit);

  console.log(
    `🔍 [DevServer] Search "${searchTerm}": ${results.length} results`
  );

  res.json({
    success: true,
    data: results,
    total: results.length,
    searchTerm,
    timestamp: new Date().toISOString(),
  });
});

// GET /api/history/:id - Get single submission
app.get("/api/history/:id", (req, res) => {
  const submissionId = req.params.id;
  const submission = mockSubmissions.find((s) => s.id === submissionId);

  if (!submission) {
    return res.status(404).json({
      success: false,
      error: "Submission not found",
      id: submissionId,
    });
  }

  console.log(`🎯 [DevServer] Serving submission: ${submissionId}`);

  res.json({
    success: true,
    data: submission,
    timestamp: new Date().toISOString(),
  });
});

// Start server
app.listen(PORT, () => {
  console.log(
    `🚀 Development History API Server running on http://localhost:${PORT}`
  );
  console.log(`📋 Available endpoints:`);
  console.log(`   GET http://localhost:${PORT}/api/history - Documentation`);
  console.log(
    `   GET http://localhost:${PORT}/api/history/recent - Recent submissions`
  );
  console.log(`   GET http://localhost:${PORT}/api/history/stats - Statistics`);
  console.log(
    `   GET http://localhost:${PORT}/api/history/search - Search submissions`
  );
  console.log(
    `   GET http://localhost:${PORT}/api/history/:id - Single submission`
  );
  console.log(``);
  console.log(`🧪 Test commands:`);
  console.log(`   curl http://localhost:${PORT}/api/history`);
  console.log(`   curl http://localhost:${PORT}/api/history/recent?limit=2`);
  console.log(`   curl http://localhost:${PORT}/api/history/stats`);
  console.log(`   curl "http://localhost:${PORT}/api/history/search?q=joao"`);
  console.log(``);
  console.log(`🎯 DGM Canvas should connect to this server automatically`);
});
