# 🔐 Problema: Dados Criptografados do Typebot

## 🚨 Problema Identificado

Os dados de nome e email estão chegando do Typebot criptografados/codificados:

```
nome: "giiLFGw5xXBCHzvp1qAbdX"  ❌ (deveria ser o nome real)
email: "v3VFChNVSCXQ2rXv4DrJ8Ah" ❌ (deveria ser o email real)
```

## 🔍 Causa do Problema

O Typebot está retornando **IDs internos de variáveis** ao invés dos **valores reais** digitados pelo usuário.

**DESCOBERTA IMPORTANTE:** Segundo a documentação do Typebot, no Script block as variáveis não são parseadas como strings, mas sim **avaliadas como JavaScript real**.

## ✅ SOLUÇÃO DEFINITIVA ENCONTRADA

### 🎯 Código Correto para o Typebot

No **último bloco do seu Typebot**, adicione um bloco **"Script"** (não "Code") com:

```javascript
// ✅ SOLUÇÃO CORRETA - Use este código no bloco Script do Typebot
console.log('=== TYPEBOT SCRIPT DEBUG ===');
console.log('Nome variável:', {{nome}});
console.log('Email variável:', {{email}});

// IMPORTANTE: {{nome}} e {{email}} são avaliados como JavaScript, não como strings!
// Não usar aspas ao redor das variáveis no Script block

window.parent.postMessage({
  type: 'typebot-completion',
  data: {
    nome: {{nome}},        // ← SEM aspas! JavaScript evaluation
    email: {{email}},      // ← SEM aspas! JavaScript evaluation
    completed: true,
    debug: {
      timestamp: new Date().toISOString(),
      method: 'script-block-evaluation'
    }
  }
}, '*');

console.log('Dados reais enviados via Script block');
```

### 🚨 O que estava errado antes

```javascript
// ❌ ERRADO - Com aspas (retorna IDs criptografados)
nome: '{{nome}}',    // Retorna: "giiLFGw5xXBCHzvp1qAbdX"
email: '{{email}}',  // Retorna: "v3VFChNVSCXQ2rXv4DrJ8Ah"

// ✅ CORRETO - Sem aspas (avalia como JavaScript)
nome: {{nome}},      // Retorna: valor real digitado
email: {{email}},    // Retorna: valor real digitado
```

### 📋 Passos para Implementar

1. **No Typebot Editor:**
   - Vá para o **último bloco** do seu fluxo
   - Adicione um bloco **"Script"** (Logic > Script)
   - ✅ **Marque "Execute on client?"** (importante!)
   - Cole o código JavaScript correto acima

2. **Certifique-se que as variáveis existem:**
   - Vá em **Variables** no painel direito
   - Confirme que `nome` e `email` estão listadas
   - ✅ Marque **"Save in results"** para ambas

3. **Nos blocos de Input:**
   - Input de Nome: deve salvar em variável `nome`
   - Input de Email: deve salvar em variável `email`

## 📖 Fundamentação na Documentação

Segundo [docs.typebot.io/editor/blocks/logic/script](https://docs.typebot.io/editor/blocks/logic/script):

> **"Variables in script are not parsed, they are evaluated."**
>
> **"You need to write `console.log({{My variable}})` instead of `console.log("{{My variable}}")`"**

Isso explica por que `'{{nome}}'` retornava IDs criptografados, mas `{{nome}}` deve retornar o valor real.

## 🧪 Como Testar

1. **Execute o fluxo** e complete o Typebot
2. **Verifique no console** se aparece:

   ```
   === TYPEBOT SCRIPT DEBUG ===
   Nome variável: João da Silva     ← Valor real esperado
   Email variável: <EMAIL>   ← Valor real esperado
   ```

3. **No sistema, deve aparecer:**

   ```
   📝 [TypebotIntegration] Extracted user info from Typebot: 
   {nome: "João da Silva", email: "<EMAIL>"}
   ```

## 📊 Status Atual

- ✅ **Sistema funcionando**: Dados chegam ao Supabase
- ✅ **Detecção implementada**: Sistema detecta dados criptografados  
- ✅ **Estrutura identificada**: Dados chegam como IDs criptografados
- ✅ **Solução encontrada**: Usar Script block sem aspas nas variáveis
- 🔧 **Implementado fallback**: Sistema usa valores informativos quando detecta criptografia

### Resultado Esperado

Após a correção no Typebot, você deve ver:

```
✅ Dados reais salvos no Supabase:
- nome: "João da Silva"  
- email: "<EMAIL>"
- patrimonio: 500
- [outros dados...]
```

## 🎯 Alternativas Se Não Funcionar

### Opção 2: Usar Set Variable antes do Script

Se o Script block ainda não funcionar:

1. **Antes do Script block, adicione 2 blocos "Set Variable":**

   **Set Variable 1:**
   - Variable: `nome_real`
   - Value: Custom → `{{nome}}`

   **Set Variable 2:**
   - Variable: `email_real`
   - Value: Custom → `{{email}}`

2. **No Script block:**

   ```javascript
   window.parent.postMessage({
     type: 'typebot-completion',
     data: {
       nome: {{nome_real}},
       email: {{email_real}},
       completed: true
     }
   }, '*');
   ```

### Opção 3: Usar HTTP Request (Webhook)

1. **Substitua o Script por "HTTP Request"**
2. **URL:** `https://httpbin.org/post` (para teste)
3. **Method:** POST
4. **Body:**

   ```json
   {
     "nome": {{nome}},
     "email": {{email}},
     "completed": true
   }
   ```

E depois interceptar no seu sistema.

---

**🔑 A chave está em usar `{{variavel}}` sem aspas no Script block, pois o Typebot avalia como JavaScript real, não como string!**

### Opção 2: Usar Webhook ao invés de postMessage

1. **No Typebot**, substitua o código JavaScript por um **HTTP Request block**
2. **Configure o webhook** para enviar para seu endpoint
3. **URL**: `https://seu-site.com/api/typebot-completion`
4. **Body**:

```json
{
  "nome": "{{nome}}",
  "email": "{{email}}",
  "sessionId": "{{session_id}}",
  "completed": true
}
```

### Opção 3: Verificar Configuração das Variáveis

1. **No editor do Typebot**:
   - Vá em "Variables"
   - Verifique se as variáveis `nome` e `email` estão configuradas corretamente
   - Certifique-se que estão marcadas como "**Save Answer**"

2. **Nos blocos de Input**:
   - Input de Nome: deve salvar em variável `nome`
   - Input de Email: deve salvar em variável `email`

## 🧪 Como Testar

1. **Execute o fluxo novamente**
2. **Verifique no console** se aparece:

   ```
   🔍 [TypebotIntegration] Complete typebotData structure: { ... }
   ```

3. **Analise a estrutura** para ver onde estão os valores reais
4. **Implemente a correção** baseada na estrutura encontrada

## 📊 Status Atual

- ✅ **Sistema funcionando**: Dados chegam ao Supabase
- ✅ **Detecção implementada**: Sistema detecta dados criptografados  
- ✅ **Estrutura identificada**: Dados chegam como IDs criptografados do Typebot
- ⚠️ **Dados ainda criptografados**: Nome e email são IDs internos do Typebot
- 🔧 **Implementado fallback**: Sistema agora usa valores informativos quando detecta criptografia

### Valores Atuais Detectados

```text
✅ RECEBIDO DO TYPEBOT:
- nome: "giiLFGw5xXBCHzvp1qAbdX" (ID interno)
- email: "v3VFChNVSCXQ2rXv4DrJ8Ah" (ID interno)

✅ AGORA SALVO NO SUPABASE:
- nome: "Nome capturado via Typebot (ID: giiLFGw5...)"
- email: "<EMAIL>"
```

## 🎯 Próximos Passos

1. **Executar teste** com logs aprimorados
2. **Analisar** a estrutura completa dos dados do Typebot
3. **Implementar** extração correta baseada na estrutura real
4. **Configurar** o Typebot para enviar dados corretos

---

**💡 Dica**: O problema é comum em integrações com Typebot. A chave está em configurar corretamente as variáveis e o método de envio dos dados.
