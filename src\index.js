/**
 * Main exports for DGM Canvas modules
 * Centralized export point for all chart and utility modules
 */

// Chart modules
export { PieChart } from "./charts/PieChart.js";

// Utility modules
export { DataManager } from "./utils/DataManager.js";
export { useDGMCanvas } from "./utils/useDGMCanvas.js";

// Component modules
export { StatusPanel } from "./components/StatusPanel.jsx";
export { WelcomeScreen } from "./components/WelcomeScreen.jsx";

// Chart factory for future expansions
export const ChartFactory = {
  createPieChart: (editor, config) => new Pie<PERSON>hart(editor, config),
  // Future: createBarChart, createLineChart, etc.
};

// Configuration constants
export const CHART_CONFIGS = {
  PIE_CHART: {
    centerX: 400,
    centerY: 350,
    radius: 120,
    colors: [
      "#FF6B6B",
      "#4ECDC4",
      "#45B7D1",
      "#96CEB4",
      "#FFEAA7",
      "#DDA0DD",
      "#98D8C8",
      "#F7DC6F",
      "#BB8FCE",
      "#85C1E9",
    ],
  },
};

// Data validation utilities
export const DataValidators = {
  validatePatrimonioData: (data) => {
    if (!data || typeof data !== "object") return false;

    const requiredFields = ["patrimonio", "ativos", "usuario"];
    for (const field of requiredFields) {
      if (!data[field]) return false;
    }

    if (!data.patrimonio.total || !data.patrimonio.alocado) return false;
    if (!data.ativos.alocacao || !Array.isArray(data.ativos.alocacao))
      return false;

    return true;
  },
};
