@echo off
REM 🚀 Script de Deploy para Windows - App Calc <PERSON>ino & DGM Canvas
REM Versão: 1.0
REM Última atualização: Janeiro 2025

setlocal enabledelayedexpansion

REM Verificar se estamos no diretório correto
if not exist "app-calc-reino" (
    echo ❌ Erro: Diretório app-calc-reino não encontrado
    echo Execute este script na raiz do workspace
    pause
    exit /b 1
)

if not exist "dgm-canvas" (
    echo ❌ Erro: Diretório dgm-canvas não encontrado
    echo Execute este script na raiz do workspace
    pause
    exit /b 1
)

echo.
echo 🚀 Deploy Automático - App Calc Reino ^& DGM Canvas
echo ==================================================
echo.

REM Verificar Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js não encontrado. Instale Node.js 18+ primeiro
    pause
    exit /b 1
)

REM Verificar pnpm
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  pnpm não encontrado. Instalando...
    npm install -g pnpm
)

REM Carregar variáveis de ambiente se existir arquivo .env
if exist ".env" (
    echo 📋 Carregando variáveis de ambiente...
    for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
        set "%%a=%%b"
    )
)

echo.
echo Escolha uma opção de deploy:
echo 1) Build local apenas (preparar para deploy)
echo 2) Deploy DGM Canvas para Vercel
echo 3) Instruções para deploy App-Calc-Reino no Render
echo 4) Build completo (ambos os projetos)
echo 5) Criar arquivos de configuração
echo 6) Sair
echo.
set /p choice="Digite sua opção (1-6): "

if "%choice%"=="1" goto build_local
if "%choice%"=="2" goto deploy_vercel
if "%choice%"=="3" goto deploy_render
if "%choice%"=="4" goto build_all
if "%choice%"=="5" goto create_configs
if "%choice%"=="6" goto end
goto invalid_option

:build_local
echo.
echo 📦 Iniciando build local...
call :build_app_calc_reino
call :build_dgm_canvas
echo ✅ Build local concluído para ambos os projetos
goto end

:deploy_vercel
echo.
echo 🚀 Deploy para Vercel...
call :build_dgm_canvas
call :deploy_to_vercel
goto end

:deploy_render
echo.
echo 📋 Preparando para deploy no Render...
call :build_app_calc_reino
call :show_render_instructions
goto end

:build_all
echo.
echo 📦 Build completo...
call :build_app_calc_reino
call :build_dgm_canvas
echo ✅ Build completo concluído
goto end

:create_configs
echo.
echo 📝 Criando arquivos de configuração...
call :create_config_files
echo ✅ Arquivos de configuração criados
goto end

:invalid_option
echo ❌ Opção inválida
goto end

REM Função para build do App-Calc-Reino
:build_app_calc_reino
echo.
echo 📦 Buildando App-Calc-Reino...
cd app-calc-reino

echo Instalando dependências...
pnpm install --frozen-lockfile
if errorlevel 1 (
    echo ❌ Erro ao instalar dependências
    cd ..
    exit /b 1
)

echo Criando build de produção...
pnpm run build
if errorlevel 1 (
    echo ❌ Erro no build
    cd ..
    exit /b 1
)

if not exist "dist" (
    echo ❌ Build não foi criado
    cd ..
    exit /b 1
)

echo ✅ App-Calc-Reino pronto para deploy
cd ..
exit /b 0

REM Função para build do DGM Canvas
:build_dgm_canvas
echo.
echo 📦 Buildando DGM Canvas...
cd dgm-canvas

echo Instalando dependências...
npm ci
if errorlevel 1 (
    echo ❌ Erro ao instalar dependências
    cd ..
    exit /b 1
)

echo Criando build de produção...
npm run build
if errorlevel 1 (
    echo ❌ Erro no build
    cd ..
    exit /b 1
)

if not exist "dist" (
    echo ❌ Build não foi criado
    cd ..
    exit /b 1
)

echo ✅ DGM Canvas pronto para deploy
cd ..
exit /b 0

REM Função para deploy no Vercel
:deploy_to_vercel
echo.
echo 🚀 Deploy para Vercel...

REM Verificar se Vercel CLI está instalado
vercel --version >nul 2>&1
if errorlevel 1 (
    echo Instalando Vercel CLI...
    npm install -g vercel
)

cd dgm-canvas
echo Fazendo deploy...
vercel --prod --confirm
if errorlevel 1 (
    echo ❌ Erro no deploy para Vercel
    cd ..
    exit /b 1
)

echo ✅ Deploy para Vercel concluído
cd ..
exit /b 0

REM Função para mostrar instruções do Render
:show_render_instructions
echo.
echo 📋 Instruções para deploy no Render:
echo.
echo 1. Acesse https://render.com e conecte seu repositório
echo 2. Configure as seguintes variáveis de ambiente:
echo    - NODE_ENV=production
echo    - SUPABASE_URL=your_supabase_url
echo    - SUPABASE_ANON_KEY=your_supabase_anon_key
echo    - SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
echo 3. Build Command: pnpm install ^&^& pnpm run build
echo 4. Start Command: pnpm run serve
echo.
echo ✅ Instruções exibidas
exit /b 0

REM Função para criar arquivos de configuração
:create_config_files
echo.
echo 📝 Criando arquivos de configuração...

REM Criar Dockerfile para app-calc-reino
if not exist "app-calc-reino\Dockerfile" (
    echo FROM node:18-alpine > app-calc-reino\Dockerfile
    echo. >> app-calc-reino\Dockerfile
    echo WORKDIR /app >> app-calc-reino\Dockerfile
    echo. >> app-calc-reino\Dockerfile
    echo # Instalar pnpm >> app-calc-reino\Dockerfile
    echo RUN npm install -g pnpm >> app-calc-reino\Dockerfile
    echo. >> app-calc-reino\Dockerfile
    echo # Copiar arquivos de dependências >> app-calc-reino\Dockerfile
    echo COPY package.json pnpm-lock.yaml ./ >> app-calc-reino\Dockerfile
    echo. >> app-calc-reino\Dockerfile
    echo # Instalar dependências >> app-calc-reino\Dockerfile
    echo RUN pnpm install --frozen-lockfile >> app-calc-reino\Dockerfile
    echo. >> app-calc-reino\Dockerfile
    echo # Copiar código fonte >> app-calc-reino\Dockerfile
    echo COPY . . >> app-calc-reino\Dockerfile
    echo. >> app-calc-reino\Dockerfile
    echo # Build da aplicação >> app-calc-reino\Dockerfile
    echo RUN pnpm run build >> app-calc-reino\Dockerfile
    echo. >> app-calc-reino\Dockerfile
    echo # Expor porta >> app-calc-reino\Dockerfile
    echo EXPOSE 3000 >> app-calc-reino\Dockerfile
    echo. >> app-calc-reino\Dockerfile
    echo # Comando de inicialização >> app-calc-reino\Dockerfile
    echo CMD ["pnpm", "run", "serve"] >> app-calc-reino\Dockerfile
    
    echo ✅ Dockerfile criado para app-calc-reino
)

REM Criar vercel.json para dgm-canvas
if not exist "dgm-canvas\vercel.json" (
    echo { > dgm-canvas\vercel.json
    echo   "framework": "vite", >> dgm-canvas\vercel.json
    echo   "buildCommand": "npm run build", >> dgm-canvas\vercel.json
    echo   "outputDirectory": "dist", >> dgm-canvas\vercel.json
    echo   "devCommand": "npm run dev", >> dgm-canvas\vercel.json
    echo   "installCommand": "npm install" >> dgm-canvas\vercel.json
    echo } >> dgm-canvas\vercel.json
    
    echo ✅ vercel.json criado para dgm-canvas
)

REM Criar arquivo .env.example
if not exist ".env.example" (
    echo # App-Calc-Reino Configuration > .env.example
    echo NODE_ENV=production >> .env.example
    echo PORT=3000 >> .env.example
    echo. >> .env.example
    echo # Supabase >> .env.example
    echo SUPABASE_URL=https://your-project.supabase.co >> .env.example
    echo SUPABASE_ANON_KEY=your_anon_key >> .env.example
    echo SUPABASE_SERVICE_ROLE_KEY=your_service_role_key >> .env.example
    echo. >> .env.example
    echo # DGM Canvas >> .env.example
    echo VITE_API_BASE_URL=https://your-app-calc-reino-domain.com >> .env.example
    echo VITE_SUPABASE_URL=https://your-project.supabase.co >> .env.example
    echo VITE_SUPABASE_ANON_KEY=your_anon_key >> .env.example
    
    echo ✅ .env.example criado
)

exit /b 0

:end
echo.
echo 🎉 Script concluído!
echo.
echo 📋 Próximos passos:
echo - Verifique os logs de deploy das plataformas
echo - Configure DNS se necessário
echo - Teste as aplicações em produção
echo - Configure monitoramento
echo.
pause
